// Script to debug the exact work experience data structure
// Run this script with: node scripts/debug_work_experience.js

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
let serviceAccount;
try {
  serviceAccount = require('./serviceAccountKey.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: serviceAccount.project_id
  });
  console.log('✅ Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Firebase Admin SDK');
  process.exit(1);
}

const db = admin.firestore();
const TEST_USER_ID = 'Bbb0ezu0zeQqCDHghn3LLGXceM93';

async function debugWorkExperience() {
  try {
    console.log('🔍 Debugging work experience data...\n');
    
    // Get the resume
    const resumesSnapshot = await db
      .collection('resumes')
      .where('userId', '==', TEST_USER_ID)
      .get();
    
    const resumeId = resumesSnapshot.docs[0].id;
    console.log(`📋 Resume ID: ${resumeId}\n`);
    
    // Get work experience subcollection
    const workExpSnapshot = await db
      .collection('resumes')
      .doc(resumeId)
      .collection('workExperience')
      .get();
    
    console.log(`📁 Found ${workExpSnapshot.docs.length} work experience items:\n`);
    
    workExpSnapshot.docs.forEach((doc, index) => {
      const data = doc.data();
      console.log(`--- Work Experience ${index + 1} ---`);
      console.log(`Document ID: ${doc.id}`);
      console.log('Raw data:');
      console.log(JSON.stringify(data, null, 2));
      
      // Check each field type
      console.log('\nField types:');
      Object.keys(data).forEach(key => {
        const value = data[key];
        console.log(`  ${key}: ${typeof value} = ${value}`);
      });
      
      // Check for problematic fields
      if (data.startDate) {
        console.log(`  startDate type: ${typeof data.startDate}`);
        if (typeof data.startDate === 'object') {
          console.log(`  startDate object: ${JSON.stringify(data.startDate)}`);
        }
      }
      
      if (data.endDate) {
        console.log(`  endDate type: ${typeof data.endDate}`);
        if (typeof data.endDate === 'object') {
          console.log(`  endDate object: ${JSON.stringify(data.endDate)}`);
        }
      }
      
      console.log('\n');
    });
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    await debugWorkExperience();
    
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

main()
  .then(() => {
    console.log('✅ Debug completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
