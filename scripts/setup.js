// Setup script to help configure Firebase test data upload
const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function setup() {
  console.log('🚀 Firebase Test Data Upload Setup\n');
  
  // Check if service account key exists
  const serviceAccountPath = path.join(__dirname, 'serviceAccountKey.json');
  const hasServiceAccount = fs.existsSync(serviceAccountPath);
  
  if (hasServiceAccount) {
    console.log('✅ Service account key file found!');
    
    // Try to read and validate the service account file
    try {
      const serviceAccount = JSON.parse(fs.readFileSync(serviceAccountPath, 'utf8'));
      console.log(`   Project ID: ${serviceAccount.project_id}`);
      console.log(`   Client Email: ${serviceAccount.client_email}`);
      
      // Update the project ID in scripts
      await updateProjectId(serviceAccount.project_id);
      
    } catch (error) {
      console.log('⚠️  Service account file exists but seems invalid');
      console.log('   Please check the JSON format');
    }
  } else {
    console.log('❌ Service account key file not found');
    console.log('\n📋 To get your service account key:');
    console.log('1. Go to Firebase Console: https://console.firebase.google.com/');
    console.log('2. Select your project');
    console.log('3. Go to Project Settings (gear icon) → Service accounts');
    console.log('4. Click "Generate new private key"');
    console.log('5. Download the JSON file');
    console.log('6. Rename it to "serviceAccountKey.json"');
    console.log('7. Place it in the scripts/ directory');
    console.log('\n');
    
    const projectId = await question('Enter your Firebase Project ID: ');
    if (projectId.trim()) {
      await updateProjectId(projectId.trim());
    }
  }
  
  // Get user ID
  console.log('\n👤 User Setup:');
  console.log('You need a valid Firebase Auth user ID to associate the test resumes with.');
  console.log('You can find user IDs in Firebase Console → Authentication → Users');
  
  const userId = await question('Enter a Firebase Auth User ID (or press Enter to use default): ');
  if (userId.trim()) {
    await updateUserId(userId.trim());
  }
  
  console.log('\n✅ Setup complete!');
  console.log('\n🚀 Next steps:');
  console.log('1. Run: npm run upload-test-data');
  console.log('2. Or run: npm run upload-multiple');
  
  rl.close();
}

async function updateProjectId(projectId) {
  const files = ['upload_test_data.js', 'upload_multiple_resumes.js'];
  
  for (const file of files) {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      let content = fs.readFileSync(filePath, 'utf8');
      content = content.replace(/projectId: 'your-project-id'/g, `projectId: '${projectId}'`);
      content = content.replace(/FIREBASE_PROJECT_ID \|\| 'your-project-id'/g, `FIREBASE_PROJECT_ID || '${projectId}'`);
      fs.writeFileSync(filePath, content);
      console.log(`   Updated ${file} with project ID: ${projectId}`);
    }
  }
}

async function updateUserId(userId) {
  const files = ['upload_test_data.js', 'upload_multiple_resumes.js'];
  
  for (const file of files) {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      let content = fs.readFileSync(filePath, 'utf8');
      content = content.replace(/const TEST_USER_ID = 'test-user-123';/g, `const TEST_USER_ID = '${userId}';`);
      fs.writeFileSync(filePath, content);
      console.log(`   Updated ${file} with user ID: ${userId}`);
    }
  }
}

// Handle errors gracefully
process.on('SIGINT', () => {
  console.log('\n\n👋 Setup cancelled');
  rl.close();
  process.exit(0);
});

// Run setup
setup().catch(error => {
  console.error('❌ Setup failed:', error.message);
  rl.close();
  process.exit(1);
});
