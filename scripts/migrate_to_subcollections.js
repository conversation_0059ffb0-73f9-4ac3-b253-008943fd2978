// Migration script to convert existing resume documents to use subcollections
// Run this script with: node scripts/migrate_to_subcollections.js

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
let serviceAccount;
try {
  serviceAccount = require('./serviceAccountKey.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: serviceAccount.project_id
  });
  console.log('✅ Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Firebase Admin SDK');
  console.error('   Make sure serviceAccountKey.json exists and is valid');
  process.exit(1);
}

const db = admin.firestore();

async function migrateToSubcollections() {
  try {
    console.log('🔄 Starting migration to subcollections...\n');

    // Get all existing resume documents
    const resumesSnapshot = await db.collection('resumes').get();

    if (resumesSnapshot.empty) {
      console.log('📭 No resumes found to migrate.');
      return;
    }

    console.log(`📋 Found ${resumesSnapshot.docs.length} resumes to migrate\n`);

    let migratedCount = 0;
    let skippedCount = 0;
    const userResumeMap = new Map(); // Track resumes per user

    for (const doc of resumesSnapshot.docs) {
      const resumeData = doc.data();
      const resumeId = doc.id;

      console.log(`Processing: ${resumeData.personalInfo?.firstName || 'Unknown'} ${resumeData.personalInfo?.lastName || 'User'} (${resumeId})`);

      // Check if this resume already uses the new structure
      if (await isAlreadyMigrated(resumeId)) {
        console.log('   ⏭️  Already migrated, skipping...');
        skippedCount++;
        continue;
      }

      // Migrate this resume
      await migrateResume(resumeId, resumeData);
      migratedCount++;

      // Track user-resume relationships
      if (resumeData.userId) {
        if (!userResumeMap.has(resumeData.userId)) {
          userResumeMap.set(resumeData.userId, []);
        }
        userResumeMap.get(resumeData.userId).push({
          resumeId,
          resumeData
        });
      }

      console.log('   ✅ Migrated successfully');
    }

    // Create/update user documents
    if (userResumeMap.size > 0) {
      console.log(`\n👥 Creating/updating ${userResumeMap.size} user documents...`);
      await createUserDocuments(userResumeMap);
    }
    
    console.log(`\n🎉 Migration completed!`);
    console.log(`   ✅ Migrated: ${migratedCount} resumes`);
    console.log(`   ⏭️  Skipped: ${skippedCount} resumes`);
    console.log(`   📊 Total: ${resumesSnapshot.docs.length} resumes`);
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    throw error;
  }
}

async function isAlreadyMigrated(resumeId) {
  try {
    // Check if any subcollections exist
    const workExpSnapshot = await db
      .collection('resumes')
      .doc(resumeId)
      .collection('workExperience')
      .limit(1)
      .get();
    
    return !workExpSnapshot.empty;
  } catch (error) {
    return false;
  }
}

async function migrateResume(resumeId, resumeData) {
  const batch = db.batch();
  
  // Create new main document structure (without arrays)
  const newMainDoc = {
    id: resumeData.id || resumeId,
    userId: resumeData.userId,
    personalInfo: resumeData.personalInfo || {},
    summary: resumeData.summary || '',
    createdAt: resumeData.createdAt || new Date().toISOString(),
    updatedAt: resumeData.updatedAt || new Date().toISOString()
  };
  
  // Update main document
  const mainDocRef = db.collection('resumes').doc(resumeId);
  batch.set(mainDocRef, newMainDoc, { merge: true });
  
  // Migrate subcollections
  const subcollections = [
    { name: 'workExperience', data: resumeData.workExperience || [] },
    { name: 'education', data: resumeData.education || [] },
    { name: 'projects', data: resumeData.projects || [] },
    { name: 'skills', data: resumeData.skills || [] },
    { name: 'languages', data: resumeData.languages || [] },
    { name: 'certifications', data: resumeData.certifications || [] },
    { name: 'socialMedia', data: resumeData.socialMedia || [] }
  ];
  
  for (const subcollection of subcollections) {
    if (Array.isArray(subcollection.data) && subcollection.data.length > 0) {
      for (const item of subcollection.data) {
        if (item.id) {
          const subDocRef = mainDocRef.collection(subcollection.name).doc(item.id);
          batch.set(subDocRef, item);
        }
      }
      console.log(`     📁 ${subcollection.name}: ${subcollection.data.length} items`);
    }
  }
  
  // Commit the batch
  await batch.commit();
}

async function createUserDocuments(userResumeMap) {
  const batch = db.batch();

  for (const [userId, userResumes] of userResumeMap) {
    // Use the first resume's personal info to create user document
    const firstResume = userResumes[0].resumeData;
    const resumeIds = userResumes.map(ur => ur.resumeId);

    const userRef = db.collection('users').doc(userId);
    const userDoc = await userRef.get();

    if (!userDoc.exists) {
      const userData = {
        id: userId,
        email: firstResume.personalInfo?.email || `user-${userId}@example.com`,
        displayName: `${firstResume.personalInfo?.firstName || 'Unknown'} ${firstResume.personalInfo?.lastName || 'User'}`,
        photoUrl: firstResume.personalInfo?.profileImageUrl || null,
        emailVerified: true,
        createdAt: firstResume.createdAt || new Date().toISOString(),
        lastSignIn: new Date().toISOString(),
        resumeIds: resumeIds
      };

      batch.set(userRef, userData);
      console.log(`   👤 Creating user: ${userData.displayName} (${resumeIds.length} resumes)`);
    } else {
      // Update existing user with resume IDs
      batch.update(userRef, {
        resumeIds: admin.firestore.FieldValue.arrayUnion(...resumeIds),
        lastSignIn: new Date().toISOString()
      });
      console.log(`   📝 Updating user: ${userId} (adding ${resumeIds.length} resumes)`);
    }
  }

  await batch.commit();
  console.log('✅ User documents created/updated');
}

async function cleanupOldStructure() {
  console.log('\n🧹 Cleaning up old structure...');
  
  const resumesSnapshot = await db.collection('resumes').get();
  const batch = db.batch();
  
  for (const doc of resumesSnapshot.docs) {
    const resumeData = doc.data();
    
    // Remove array fields from main document
    const fieldsToRemove = [
      'workExperience',
      'education', 
      'projects',
      'skills',
      'languages',
      'certifications',
      'socialMedia'
    ];
    
    const updates = {};
    let hasFieldsToRemove = false;
    
    for (const field of fieldsToRemove) {
      if (resumeData[field] !== undefined) {
        updates[field] = admin.firestore.FieldValue.delete();
        hasFieldsToRemove = true;
      }
    }
    
    if (hasFieldsToRemove) {
      batch.update(doc.ref, updates);
    }
  }
  
  await batch.commit();
  console.log('✅ Cleanup completed');
}

// Main execution
async function main() {
  try {
    await migrateToSubcollections();
    
    const shouldCleanup = process.argv.includes('--cleanup');
    if (shouldCleanup) {
      await cleanupOldStructure();
    } else {
      console.log('\n💡 To remove old array fields from main documents, run:');
      console.log('   node migrate_to_subcollections.js --cleanup');
    }
    
    console.log('\n🎯 Migration benefits:');
    console.log('   • Better query performance');
    console.log('   • Reduced document size');
    console.log('   • More scalable structure');
    console.log('   • Easier to manage individual sections');
    
  } catch (error) {
    console.error('❌ Migration script failed:', error);
    process.exit(1);
  }
}

main()
  .then(() => {
    console.log('\n✅ Script completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
