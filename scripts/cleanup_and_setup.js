// <PERSON>ript to clean up existing data and set up with correct user ID
// Run this script with: node scripts/cleanup_and_setup.js

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
let serviceAccount;
try {
  serviceAccount = require('./serviceAccountKey.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: serviceAccount.project_id
  });
  console.log('✅ Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Firebase Admin SDK');
  console.error('   Make sure serviceAccountKey.json exists and is valid');
  process.exit(1);
}

const db = admin.firestore();
const CORRECT_USER_ID = 'Bbb0ezu0zeQqCDHghn3LLGXceM93';

async function cleanupExistingData() {
  try {
    console.log('🧹 Cleaning up existing data...\n');
    
    // Get all existing resumes
    const resumesSnapshot = await db.collection('resumes').get();
    console.log(`📋 Found ${resumesSnapshot.docs.length} existing resumes`);
    
    if (resumesSnapshot.docs.length > 0) {
      const batch = db.batch();
      
      for (const doc of resumesSnapshot.docs) {
        // Delete subcollections first
        await deleteSubcollections(doc.id);
        
        // Add main document to batch delete
        batch.delete(doc.ref);
      }
      
      await batch.commit();
      console.log('   ✅ Deleted all existing resumes');
    }
    
    // Get all existing users
    const usersSnapshot = await db.collection('users').get();
    console.log(`👥 Found ${usersSnapshot.docs.length} existing users`);
    
    if (usersSnapshot.docs.length > 0) {
      const batch = db.batch();
      
      for (const doc of usersSnapshot.docs) {
        batch.delete(doc.ref);
      }
      
      await batch.commit();
      console.log('   ✅ Deleted all existing users');
    }
    
    console.log('\n🎯 Database cleaned successfully!');
    
  } catch (error) {
    console.error('❌ Cleanup failed:', error.message);
    throw error;
  }
}

async function deleteSubcollections(resumeId) {
  const subcollectionNames = [
    'workExperience',
    'education',
    'projects',
    'skills',
    'languages',
    'certifications',
    'socialMedia'
  ];

  for (const collectionName of subcollectionNames) {
    const querySnapshot = await db
      .collection('resumes')
      .doc(resumeId)
      .collection(collectionName)
      .get();

    if (querySnapshot.docs.length > 0) {
      const batch = db.batch();
      for (const doc of querySnapshot.docs) {
        batch.delete(doc.reference);
      }
      await batch.commit();
    }
  }
}

async function createCorrectUser() {
  try {
    console.log(`👤 Creating user with correct ID: ${CORRECT_USER_ID}`);
    
    const userData = {
      id: CORRECT_USER_ID,
      email: '<EMAIL>',
      displayName: 'Test User',
      photoUrl: null,
      emailVerified: true,
      createdAt: new Date().toISOString(),
      lastSignIn: new Date().toISOString(),
      resumeIds: []
    };
    
    await db.collection('users').doc(CORRECT_USER_ID).set(userData);
    console.log('   ✅ User created successfully');
    
  } catch (error) {
    console.error('❌ Failed to create user:', error.message);
    throw error;
  }
}

async function updateExistingResumesToCorrectUser() {
  try {
    console.log(`🔄 Updating any existing resumes to use correct user ID...`);
    
    const resumesSnapshot = await db.collection('resumes').get();
    
    if (resumesSnapshot.docs.length === 0) {
      console.log('   📭 No existing resumes to update');
      return;
    }
    
    const batch = db.batch();
    let updatedCount = 0;
    
    for (const doc of resumesSnapshot.docs) {
      const resumeData = doc.data();
      
      if (resumeData.userId !== CORRECT_USER_ID) {
        batch.update(doc.ref, { userId: CORRECT_USER_ID });
        updatedCount++;
      }
    }
    
    if (updatedCount > 0) {
      await batch.commit();
      console.log(`   ✅ Updated ${updatedCount} resumes to correct user ID`);
      
      // Update user's resumeIds array
      const resumeIds = resumesSnapshot.docs.map(doc => doc.id);
      await db.collection('users').doc(CORRECT_USER_ID).update({
        resumeIds: resumeIds,
        lastSignIn: new Date().toISOString()
      });
      console.log(`   ✅ Updated user's resume list with ${resumeIds.length} resumes`);
    } else {
      console.log('   ✅ All resumes already have correct user ID');
    }
    
  } catch (error) {
    console.error('❌ Failed to update resumes:', error.message);
    throw error;
  }
}

async function verifySetup() {
  try {
    console.log('\n🔍 Verifying setup...');
    
    // Check user exists
    const userDoc = await db.collection('users').doc(CORRECT_USER_ID).get();
    if (userDoc.exists) {
      const userData = userDoc.data();
      console.log(`   ✅ User exists: ${userData.displayName} (${userData.email})`);
      console.log(`   📋 Resume count: ${userData.resumeIds?.length || 0}`);
    } else {
      console.log(`   ❌ User ${CORRECT_USER_ID} not found`);
    }
    
    // Check resumes
    const resumesSnapshot = await db
      .collection('resumes')
      .where('userId', '==', CORRECT_USER_ID)
      .get();
    
    console.log(`   📋 Resumes for this user: ${resumesSnapshot.docs.length}`);
    
    if (resumesSnapshot.docs.length > 0) {
      for (const doc of resumesSnapshot.docs) {
        const resumeData = doc.data();
        console.log(`     - ${resumeData.personalInfo?.firstName || 'Unknown'} ${resumeData.personalInfo?.lastName || 'User'} (${doc.id})`);
      }
    }
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    const shouldCleanup = process.argv.includes('--cleanup');
    const shouldUpdate = process.argv.includes('--update-existing');
    
    if (shouldCleanup) {
      await cleanupExistingData();
      await createCorrectUser();
    } else if (shouldUpdate) {
      await createCorrectUser();
      await updateExistingResumesToCorrectUser();
    } else {
      console.log('🎯 Setting up with correct user ID...\n');
      await createCorrectUser();
    }
    
    await verifySetup();
    
    console.log('\n🎉 Setup completed successfully!');
    console.log(`\n📝 Next steps:`);
    console.log(`   1. Run: npm run upload-test-data`);
    console.log(`   2. Or run: npm run upload-multiple`);
    console.log(`   3. Verify: npm run verify-links`);
    
    console.log(`\n💡 Options:`);
    console.log(`   --cleanup: Delete all existing data first`);
    console.log(`   --update-existing: Keep existing resumes but assign to correct user`);
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
}

main()
  .then(() => {
    console.log('\n✅ Script completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
