// <PERSON>ript to test if <PERSON><PERSON><PERSON> can connect to Firebase with the same configuration
// Run this script with: node scripts/test_flutter_connection.js

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
let serviceAccount;
try {
  serviceAccount = require('./serviceAccountKey.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: serviceAccount.project_id
  });
  console.log('✅ Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Firebase Admin SDK');
  console.error('   Make sure serviceAccountKey.json exists and is valid');
  process.exit(1);
}

const db = admin.firestore();
const TEST_USER_ID = 'Bbb0ezu0zeQqCDHghn3LLGXceM93';

async function testFlutterConnection() {
  try {
    console.log('🔍 Testing Flutter-like connection...\n');
    
    // Test 1: Simulate getUserResumes call exactly like Flutter does
    console.log('1. Testing getUserResumes query...');
    
    const querySnapshot = await db
      .collection('resumes')
      .where('userId', '==', TEST_USER_ID)
      .get();
    
    console.log(`   📋 Found ${querySnapshot.docs.length} resumes`);
    
    if (querySnapshot.docs.length === 0) {
      console.log('   ❌ No resumes found - this is why Flutter shows empty list');
      return;
    }
    
    // Test 2: Process each resume like Flutter does
    console.log('\n2. Processing resumes like Flutter...');
    
    const resumes = [];
    
    for (const doc of querySnapshot.docs) {
      const data = doc.data();
      
      console.log(`   📝 Processing resume: ${doc.id}`);
      console.log(`   👤 User ID: ${data.userId}`);
      console.log(`   📧 Email: ${data.personalInfo?.email}`);
      
      // Add empty arrays for subcollections like Flutter does
      const resumeData = {
        ...data,
        'workExperience': [],
        'education': [],
        'projects': [],
        'skills': [],
        'languages': [],
        'certifications': [],
        'socialMedia': [],
      };
      
      resumes.push(resumeData);
      console.log(`   ✅ Resume processed successfully`);
    }
    
    console.log(`\n📊 Total resumes processed: ${resumes.length}`);
    
    // Test 3: Check if data structure matches Flutter expectations
    console.log('\n3. Checking Flutter model compatibility...');
    
    for (const resume of resumes) {
      console.log(`\n   📋 Resume: ${resume.id}`);
      
      // Check required fields
      const requiredFields = ['id', 'userId', 'personalInfo', 'summary', 'createdAt', 'updatedAt'];
      const missingFields = requiredFields.filter(field => !(field in resume));
      
      if (missingFields.length === 0) {
        console.log('   ✅ All required fields present');
      } else {
        console.log(`   ❌ Missing fields: ${missingFields.join(', ')}`);
      }
      
      // Check personalInfo structure
      if (resume.personalInfo && typeof resume.personalInfo === 'object') {
        const personalInfoFields = ['firstName', 'lastName', 'email'];
        const missingPersonalFields = personalInfoFields.filter(field => !(field in resume.personalInfo));
        
        if (missingPersonalFields.length === 0) {
          console.log('   ✅ Personal info complete');
        } else {
          console.log(`   ❌ Missing personal info: ${missingPersonalFields.join(', ')}`);
        }
      } else {
        console.log('   ❌ Personal info is not an object');
      }
      
      // Check date formats
      if (resume.createdAt && typeof resume.createdAt === 'string') {
        try {
          new Date(resume.createdAt);
          console.log('   ✅ createdAt is valid date string');
        } catch (e) {
          console.log('   ❌ createdAt is not a valid date string');
        }
      } else {
        console.log('   ❌ createdAt is not a string');
      }
      
      if (resume.updatedAt && typeof resume.updatedAt === 'string') {
        try {
          new Date(resume.updatedAt);
          console.log('   ✅ updatedAt is valid date string');
        } catch (e) {
          console.log('   ❌ updatedAt is not a valid date string');
        }
      } else {
        console.log('   ❌ updatedAt is not a string');
      }
    }
    
    // Test 4: Simulate getResume call for detailed view
    console.log('\n4. Testing getResume with subcollections...');
    
    const firstResumeId = querySnapshot.docs[0].id;
    console.log(`   📋 Testing resume: ${firstResumeId}`);
    
    // Get main document
    const resumeDoc = await db.collection('resumes').doc(firstResumeId).get();
    const mainData = resumeDoc.data();
    
    // Get subcollections
    const subcollections = [
      'workExperience',
      'education',
      'projects',
      'skills',
      'languages',
      'certifications',
      'socialMedia'
    ];
    
    const subcollectionData = {};
    
    for (const collectionName of subcollections) {
      const snapshot = await db
        .collection('resumes')
        .doc(firstResumeId)
        .collection(collectionName)
        .get();
      
      subcollectionData[collectionName] = snapshot.docs.map(doc => doc.data());
      console.log(`   📁 ${collectionName}: ${snapshot.docs.length} items`);
    }
    
    // Combine data like Flutter does
    const completeResume = {
      ...mainData,
      ...subcollectionData
    };
    
    console.log(`   ✅ Complete resume has ${Object.keys(completeResume).length} fields`);
    
    // Test 5: Check Firebase project configuration
    console.log('\n5. Checking Firebase project configuration...');
    console.log(`   🏗️  Project ID: ${serviceAccount.project_id}`);
    console.log(`   🔑 Client Email: ${serviceAccount.client_email}`);
    console.log(`   📅 Private Key ID: ${serviceAccount.private_key_id}`);
    
    console.log('\n🎉 All tests passed! Firebase connection should work for Flutter.');
    console.log('\n📱 If Flutter still shows no resumes, check:');
    console.log('   1. Flutter Firebase configuration (google-services.json/GoogleService-Info.plist)');
    console.log('   2. Flutter console for error messages');
    console.log('   3. Authentication state in Flutter app');
    console.log('   4. Network connectivity in Flutter app');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
    
    if (error.message.includes('permission-denied')) {
      console.log('\n💡 Permission denied error suggests:');
      console.log('   1. Check Firestore security rules');
      console.log('   2. Verify authentication is working');
      console.log('   3. Check if user has proper permissions');
    }
    
    if (error.message.includes('not-found')) {
      console.log('\n💡 Not found error suggests:');
      console.log('   1. Collection or document does not exist');
      console.log('   2. Check collection names and document IDs');
      console.log('   3. Verify data was uploaded correctly');
    }
    
    throw error;
  }
}

// Main execution
async function main() {
  try {
    await testFlutterConnection();
    
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

main()
  .then(() => {
    console.log('\n✅ Test completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
