// Firebase Admin SDK script to upload multiple test resumes to Firestore
// Run this script with: node scripts/upload_multiple_resumes.js

const admin = require('firebase-admin');
const { v4: uuidv4 } = require('uuid');

// Initialize Firebase Admin SDK
let serviceAccount;
try {
  serviceAccount = require('./serviceAccountKey.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: serviceAccount.project_id // Use project ID from service account
  });
  console.log('✅ Initialized with service account key file');
} catch (error) {
  console.log('⚠️  Service account key file not found. Trying environment variable...');

  if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
    admin.initializeApp({
      projectId: process.env.FIREBASE_PROJECT_ID || 'resume-d24cb'
    });
    console.log('✅ Initialized with environment variable');
  } else {
    console.error('❌ Firebase initialization failed!');
    console.error('Please either:');
    console.error('1. Download serviceAccountKey.json from Firebase Console, or');
    console.error('2. Set GOOGLE_APPLICATION_CREDENTIALS environment variable');
    console.error('\nSee setup instructions in README.md');
    process.exit(1);
  }
}

const db = admin.firestore();

// Test user ID - replace with actual user ID
const TEST_USER_ID = 'Bbb0ezu0zeQqCDHghn3LLGXceM93';

// Multiple test resume profiles
const testResumes = [
  {
    id: uuidv4(),
    userId: TEST_USER_ID,
    personalInfo: {
      firstName: 'Michael',
      lastName: 'Chen',
      email: '<EMAIL>',
      phone: '+****************',
      address: '456 Developer Lane',
      city: 'Seattle',
      state: 'Washington',
      zipCode: '98101',
      country: 'United States',
      profileImageUrl: null
    },
    summary: 'Recent Computer Science graduate with strong foundation in software development. Passionate about learning new technologies and contributing to innovative projects.',
    workExperience: [
      {
        id: uuidv4(),
        jobTitle: 'Software Engineering Intern',
        company: 'Microsoft',
        location: 'Redmond, WA',
        startDate: '2023-06-01T00:00:00.000Z',
        endDate: '2023-08-31T00:00:00.000Z',
        isCurrentJob: false,
        description: 'Worked on Azure cloud services team developing internal tools.',
        achievements: [
          'Developed automation tool that saved 10 hours/week for team',
          'Contributed to 3 major feature releases',
          'Received excellent performance review'
        ]
      }
    ],
    education: [
      {
        id: uuidv4(),
        degree: 'Bachelor of Science in Computer Science',
        institution: 'University of Washington',
        location: 'Seattle, WA',
        startDate: '2019-09-01T00:00:00.000Z',
        endDate: '2023-06-15T00:00:00.000Z',
        isCurrentlyStudying: false,
        gpa: '3.7',
        description: 'Graduated Magna Cum Laude. Focus on Software Engineering and Data Structures.'
      }
    ],
    projects: [
      {
        id: uuidv4(),
        name: 'Campus Food Delivery App',
        description: 'Flutter mobile app for food delivery within university campus.',
        technologies: ['Flutter', 'Firebase', 'Google Maps API'],
        githubUrl: 'https://github.com/michaelchen/campus-food',
        startDate: '2023-01-01T00:00:00.000Z',
        endDate: '2023-05-15T00:00:00.000Z',
        achievements: [
          'Used by 500+ students during beta testing',
          'Integrated with 5 campus restaurants',
          'Won 2nd place in university hackathon'
        ]
      }
    ],
    skills: [
      {
        id: uuidv4(),
        category: 'Programming Languages',
        skills: [
          { id: uuidv4(), name: 'Java', proficiencyLevel: 4 },
          { id: uuidv4(), name: 'Python', proficiencyLevel: 3 },
          { id: uuidv4(), name: 'Dart', proficiencyLevel: 3 },
          { id: uuidv4(), name: 'JavaScript', proficiencyLevel: 3 }
        ]
      }
    ],
    languages: [
      { id: uuidv4(), language: 'English', proficiency: 'Native' },
      { id: uuidv4(), language: 'Mandarin', proficiency: 'Native' }
    ],
    certifications: [],
    socialMedia: [
      {
        id: uuidv4(),
        platform: 'GitHub',
        url: 'https://github.com/michaelchen',
        username: 'michaelchen'
      }
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },

  {
    id: uuidv4(),
    userId: TEST_USER_ID,
    personalInfo: {
      firstName: 'Emily',
      lastName: 'Rodriguez',
      email: '<EMAIL>',
      phone: '+****************',
      address: '789 Code Street',
      city: 'Austin',
      state: 'Texas',
      zipCode: '73301',
      country: 'United States',
      profileImageUrl: null
    },
    summary: 'Full-Stack Developer with 3 years of experience building scalable web applications. Expertise in React, Node.js, and cloud technologies. Strong problem-solving skills and passion for clean, maintainable code.',
    workExperience: [
      {
        id: uuidv4(),
        jobTitle: 'Full-Stack Developer',
        company: 'TechStart Inc',
        location: 'Austin, TX',
        startDate: '2022-01-15T00:00:00.000Z',
        endDate: null,
        isCurrentJob: true,
        description: 'Develop and maintain web applications using React and Node.js.',
        achievements: [
          'Led development of customer portal serving 10K+ users',
          'Reduced page load times by 40% through optimization',
          'Implemented automated testing increasing code coverage to 85%'
        ]
      },
      {
        id: uuidv4(),
        jobTitle: 'Junior Web Developer',
        company: 'Digital Agency Pro',
        location: 'Austin, TX',
        startDate: '2021-06-01T00:00:00.000Z',
        endDate: '2022-01-10T00:00:00.000Z',
        isCurrentJob: false,
        description: 'Built responsive websites and web applications for various clients.',
        achievements: [
          'Delivered 15+ client projects on time and within budget',
          'Improved website performance scores by average of 30%',
          'Collaborated with design team on 20+ projects'
        ]
      }
    ],
    education: [
      {
        id: uuidv4(),
        degree: 'Bachelor of Arts in Computer Science',
        institution: 'University of Texas at Austin',
        location: 'Austin, TX',
        startDate: '2017-08-01T00:00:00.000Z',
        endDate: '2021-05-15T00:00:00.000Z',
        isCurrentlyStudying: false,
        gpa: '3.6',
        description: 'Minor in Business Administration. Active in Women in Tech organization.'
      }
    ],
    projects: [
      {
        id: uuidv4(),
        name: 'Recipe Sharing Platform',
        description: 'Full-stack web application for sharing and discovering recipes.',
        technologies: ['React', 'Node.js', 'MongoDB', 'Express.js'],
        projectUrl: 'https://recipe-share-platform.com',
        githubUrl: 'https://github.com/emilyrodriguez/recipe-platform',
        startDate: '2022-09-01T00:00:00.000Z',
        endDate: '2023-01-15T00:00:00.000Z',
        achievements: [
          '1000+ registered users',
          'Featured in local tech newsletter',
          'Implemented advanced search and filtering'
        ]
      }
    ],
    skills: [
      {
        id: uuidv4(),
        category: 'Frontend',
        skills: [
          { id: uuidv4(), name: 'React', proficiencyLevel: 4 },
          { id: uuidv4(), name: 'JavaScript', proficiencyLevel: 4 },
          { id: uuidv4(), name: 'HTML/CSS', proficiencyLevel: 5 },
          { id: uuidv4(), name: 'TypeScript', proficiencyLevel: 3 }
        ]
      },
      {
        id: uuidv4(),
        category: 'Backend',
        skills: [
          { id: uuidv4(), name: 'Node.js', proficiencyLevel: 4 },
          { id: uuidv4(), name: 'Express.js', proficiencyLevel: 4 },
          { id: uuidv4(), name: 'MongoDB', proficiencyLevel: 3 },
          { id: uuidv4(), name: 'PostgreSQL', proficiencyLevel: 3 }
        ]
      }
    ],
    languages: [
      { id: uuidv4(), language: 'English', proficiency: 'Native' },
      { id: uuidv4(), language: 'Spanish', proficiency: 'Native' }
    ],
    certifications: [
      {
        id: uuidv4(),
        name: 'React Developer Certification',
        issuer: 'Meta',
        issueDate: '2022-08-15T00:00:00.000Z',
        expiryDate: null,
        credentialId: 'META-REACT-2022-445566',
        credentialUrl: 'https://developers.facebook.com/certification/'
      }
    ],
    socialMedia: [
      {
        id: uuidv4(),
        platform: 'LinkedIn',
        url: 'https://linkedin.com/in/emily-rodriguez-dev',
        username: 'emily-rodriguez-dev'
      },
      {
        id: uuidv4(),
        platform: 'GitHub',
        url: 'https://github.com/emilyrodriguez',
        username: 'emilyrodriguez'
      }
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },

  {
    id: uuidv4(),
    userId: TEST_USER_ID,
    personalInfo: {
      firstName: 'David',
      lastName: 'Kim',
      email: '<EMAIL>',
      phone: '+****************',
      address: '321 Backend Blvd',
      city: 'Denver',
      state: 'Colorado',
      zipCode: '80201',
      country: 'United States',
      profileImageUrl: null
    },
    summary: 'Senior Backend Engineer with 7+ years of experience designing and implementing scalable distributed systems. Expert in microservices architecture, cloud platforms, and DevOps practices. Proven track record of leading technical teams and delivering high-performance solutions.',
    workExperience: [
      {
        id: uuidv4(),
        jobTitle: 'Senior Backend Engineer',
        company: 'CloudTech Solutions',
        location: 'Denver, CO',
        startDate: '2021-03-01T00:00:00.000Z',
        endDate: null,
        isCurrentJob: true,
        description: 'Lead backend development for cloud-native applications serving millions of users.',
        achievements: [
          'Architected microservices handling 1M+ requests/day',
          'Reduced system latency by 60% through optimization',
          'Led team of 6 engineers across 3 time zones',
          'Implemented monitoring reducing downtime by 80%'
        ]
      },
      {
        id: uuidv4(),
        jobTitle: 'Backend Developer',
        company: 'FinTech Innovations',
        location: 'Denver, CO',
        startDate: '2019-01-15T00:00:00.000Z',
        endDate: '2021-02-28T00:00:00.000Z',
        isCurrentJob: false,
        description: 'Developed secure financial APIs and payment processing systems.',
        achievements: [
          'Built payment system processing $10M+ monthly',
          'Achieved PCI DSS compliance for all systems',
          'Reduced API response time by 45%',
          'Mentored 3 junior developers'
        ]
      }
    ],
    education: [
      {
        id: uuidv4(),
        degree: 'Master of Science in Computer Science',
        institution: 'Colorado School of Mines',
        location: 'Golden, CO',
        startDate: '2015-08-01T00:00:00.000Z',
        endDate: '2017-05-15T00:00:00.000Z',
        isCurrentlyStudying: false,
        gpa: '3.9',
        description: 'Specialization in Distributed Systems and Network Security.'
      }
    ],
    projects: [
      {
        id: uuidv4(),
        name: 'Distributed Cache System',
        description: 'High-performance distributed caching system built with Go and Redis.',
        technologies: ['Go', 'Redis', 'Docker', 'Kubernetes', 'gRPC'],
        githubUrl: 'https://github.com/davidkim/distributed-cache',
        startDate: '2023-03-01T00:00:00.000Z',
        endDate: '2023-08-30T00:00:00.000Z',
        achievements: [
          'Handles 100K+ operations per second',
          'Used in production by 3 major clients',
          'Open-sourced with 500+ GitHub stars'
        ]
      }
    ],
    skills: [
      {
        id: uuidv4(),
        category: 'Programming Languages',
        skills: [
          { id: uuidv4(), name: 'Go', proficiencyLevel: 5 },
          { id: uuidv4(), name: 'Python', proficiencyLevel: 5 },
          { id: uuidv4(), name: 'Java', proficiencyLevel: 4 },
          { id: uuidv4(), name: 'Rust', proficiencyLevel: 3 }
        ]
      },
      {
        id: uuidv4(),
        category: 'Infrastructure',
        skills: [
          { id: uuidv4(), name: 'Kubernetes', proficiencyLevel: 5 },
          { id: uuidv4(), name: 'Docker', proficiencyLevel: 5 },
          { id: uuidv4(), name: 'AWS', proficiencyLevel: 4 },
          { id: uuidv4(), name: 'Terraform', proficiencyLevel: 4 }
        ]
      }
    ],
    languages: [
      { id: uuidv4(), language: 'English', proficiency: 'Native' },
      { id: uuidv4(), language: 'Korean', proficiency: 'Native' }
    ],
    certifications: [
      {
        id: uuidv4(),
        name: 'AWS Solutions Architect - Professional',
        issuer: 'Amazon Web Services',
        issueDate: '2022-06-20T00:00:00.000Z',
        expiryDate: '2025-06-20T00:00:00.000Z',
        credentialId: 'AWS-SAP-2022-778899',
        credentialUrl: 'https://aws.amazon.com/certification/certified-solutions-architect-professional/'
      },
      {
        id: uuidv4(),
        name: 'Certified Kubernetes Administrator',
        issuer: 'Cloud Native Computing Foundation',
        issueDate: '2023-02-10T00:00:00.000Z',
        expiryDate: '2026-02-10T00:00:00.000Z',
        credentialId: 'CKA-2023-334455',
        credentialUrl: 'https://www.cncf.io/certification/cka/'
      }
    ],
    socialMedia: [
      {
        id: uuidv4(),
        platform: 'LinkedIn',
        url: 'https://linkedin.com/in/david-kim-backend',
        username: 'david-kim-backend'
      },
      {
        id: uuidv4(),
        platform: 'GitHub',
        url: 'https://github.com/davidkim',
        username: 'davidkim'
      }
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// Function to upload multiple resumes using separate collections
async function uploadMultipleResumes() {
  try {
    console.log('Starting to upload multiple test resumes...');

    for (let i = 0; i < testResumes.length; i++) {
      const resume = testResumes[i];
      console.log(`\nUploading resume ${i + 1}/${testResumes.length}: ${resume.personalInfo.firstName} ${resume.personalInfo.lastName}`);

      // Create or update user document
      await createOrUpdateUser(resume.userId, resume);

      // Upload main resume document
      await db.collection('resumes').doc(resume.id).set({
        id: resume.id,
        userId: resume.userId,
        personalInfo: resume.personalInfo,
        summary: resume.summary,
        createdAt: resume.createdAt,
        updatedAt: resume.updatedAt
      });

      // Upload subcollections using batches for better performance
      await uploadSubcollections(resume);

      // Add resume to user's resume list
      await addResumeToUser(resume.userId, resume.id);

      console.log(`   ✅ Uploaded ${resume.personalInfo.firstName} ${resume.personalInfo.lastName} with all subcollections`);
    }
    
    console.log('\n🎉 Successfully uploaded all test resumes!');
    console.log(`Total resumes: ${testResumes.length}`);
    console.log(`User ID: ${TEST_USER_ID}`);
    console.log('\nResume IDs:');
    testResumes.forEach((resume, index) => {
      console.log(`${index + 1}. ${resume.personalInfo.firstName} ${resume.personalInfo.lastName}: ${resume.id}`);
    });
    
  } catch (error) {
    console.error('❌ Error uploading test resumes:', error.message);

    // Provide specific guidance for common errors
    if (error.message.includes('Cloud Firestore API has not been used')) {
      console.error('\n🔧 SOLUTION:');
      console.error('1. Go to Firebase Console: https://console.firebase.google.com/');
      console.error('2. Select your project');
      console.error('3. Go to "Firestore Database" in the sidebar');
      console.error('4. Click "Create database"');
      console.error('5. Choose "Start in test mode"');
      console.error('6. Select a location and create');
      console.error('7. Wait a few minutes, then retry this script');
    } else if (error.message.includes('PERMISSION_DENIED')) {
      console.error('\n🔧 SOLUTION:');
      console.error('1. Check your Firebase project ID is correct');
      console.error('2. Verify your service account key has Firestore permissions');
      console.error('3. Make sure Firestore is enabled in your project');
    }

    throw error;
  }
}

// Helper function to upload subcollections for a resume
async function uploadSubcollections(resume) {
  const collections = [
    { name: 'workExperience', data: resume.workExperience },
    { name: 'education', data: resume.education },
    { name: 'projects', data: resume.projects },
    { name: 'skills', data: resume.skills },
    { name: 'languages', data: resume.languages },
    { name: 'certifications', data: resume.certifications },
    { name: 'socialMedia', data: resume.socialMedia }
  ];

  for (const collection of collections) {
    if (collection.data && collection.data.length > 0) {
      const batch = db.batch();
      collection.data.forEach(item => {
        const docRef = db.collection('resumes').doc(resume.id).collection(collection.name).doc(item.id);
        batch.set(docRef, item);
      });
      await batch.commit();
      console.log(`     📁 ${collection.name}: ${collection.data.length} items`);
    }
  }
}

// Helper function to create or update user document
async function createOrUpdateUser(userId, resumeData) {
  try {
    const userRef = db.collection('users').doc(userId);
    const userDoc = await userRef.get();

    if (!userDoc.exists) {
      // Create new user document
      const userData = {
        id: userId,
        email: resumeData.personalInfo.email,
        displayName: `${resumeData.personalInfo.firstName} ${resumeData.personalInfo.lastName}`,
        photoUrl: resumeData.personalInfo.profileImageUrl || null,
        emailVerified: true, // Assume verified for test data
        createdAt: new Date().toISOString(),
        lastSignIn: new Date().toISOString(),
        resumeIds: [] // Will be updated separately
      };

      await userRef.set(userData);
      console.log('     👤 Created user document');
    }
  } catch (error) {
    console.error('     ❌ Failed to create/update user:', error.message);
    throw error;
  }
}

// Helper function to add resume ID to user's resume list
async function addResumeToUser(userId, resumeId) {
  try {
    const userRef = db.collection('users').doc(userId);

    // Use arrayUnion to add resume ID if it doesn't already exist
    await userRef.update({
      resumeIds: admin.firestore.FieldValue.arrayUnion(resumeId),
      lastSignIn: new Date().toISOString() // Update last activity
    });

    console.log('     📋 Added resume to user\'s list');
  } catch (error) {
    console.error('     ❌ Failed to add resume to user:', error.message);
    throw error;
  }
}

// Run the upload function
uploadMultipleResumes()
  .then(() => {
    console.log('\nScript completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
