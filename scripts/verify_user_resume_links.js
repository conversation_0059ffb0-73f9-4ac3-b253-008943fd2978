// Script to verify user-resume relationships in Firestore
// Run this script with: node scripts/verify_user_resume_links.js

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
let serviceAccount;
try {
  serviceAccount = require('./serviceAccountKey.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: serviceAccount.project_id
  });
  console.log('✅ Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Firebase Admin SDK');
  console.error('   Make sure serviceAccountKey.json exists and is valid');
  process.exit(1);
}

const db = admin.firestore();

async function verifyUserResumeLinks() {
  try {
    console.log('🔍 Verifying user-resume relationships...\n');
    
    // Get all users
    const usersSnapshot = await db.collection('users').get();
    const resumesSnapshot = await db.collection('resumes').get();
    
    console.log(`👥 Found ${usersSnapshot.docs.length} users`);
    console.log(`📋 Found ${resumesSnapshot.docs.length} resumes\n`);
    
    // Create maps for easier lookup
    const userMap = new Map();
    const resumeMap = new Map();
    
    usersSnapshot.docs.forEach(doc => {
      userMap.set(doc.id, doc.data());
    });
    
    resumesSnapshot.docs.forEach(doc => {
      resumeMap.set(doc.id, doc.data());
    });
    
    // Verify each user's resume references
    let totalIssues = 0;
    
    for (const [userId, userData] of userMap) {
      console.log(`👤 User: ${userData.displayName || userData.email} (${userId})`);
      console.log(`   📧 Email: ${userData.email}`);
      console.log(`   📋 Resume IDs: ${userData.resumeIds?.length || 0}`);
      
      if (userData.resumeIds && userData.resumeIds.length > 0) {
        for (const resumeId of userData.resumeIds) {
          if (resumeMap.has(resumeId)) {
            const resumeData = resumeMap.get(resumeId);
            if (resumeData.userId === userId) {
              console.log(`     ✅ ${resumeId} - Valid link`);
            } else {
              console.log(`     ❌ ${resumeId} - Resume belongs to different user: ${resumeData.userId}`);
              totalIssues++;
            }
          } else {
            console.log(`     ❌ ${resumeId} - Resume not found`);
            totalIssues++;
          }
        }
      } else {
        console.log('     📭 No resumes linked');
      }
      console.log('');
    }
    
    // Check for orphaned resumes (resumes without user references)
    console.log('🔍 Checking for orphaned resumes...\n');
    
    for (const [resumeId, resumeData] of resumeMap) {
      const userId = resumeData.userId;
      
      if (!userId) {
        console.log(`❌ Resume ${resumeId} has no userId`);
        totalIssues++;
        continue;
      }
      
      if (!userMap.has(userId)) {
        console.log(`❌ Resume ${resumeId} references non-existent user: ${userId}`);
        totalIssues++;
        continue;
      }
      
      const userData = userMap.get(userId);
      if (!userData.resumeIds || !userData.resumeIds.includes(resumeId)) {
        console.log(`❌ Resume ${resumeId} not in user ${userId}'s resume list`);
        totalIssues++;
      }
    }
    
    // Summary
    console.log('\n📊 Summary:');
    console.log(`   👥 Users: ${usersSnapshot.docs.length}`);
    console.log(`   📋 Resumes: ${resumesSnapshot.docs.length}`);
    console.log(`   ❌ Issues found: ${totalIssues}`);
    
    if (totalIssues === 0) {
      console.log('\n🎉 All user-resume relationships are valid!');
    } else {
      console.log('\n⚠️  Issues found. Consider running the fix script.');
    }
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    throw error;
  }
}

async function fixUserResumeLinks() {
  try {
    console.log('🔧 Fixing user-resume relationships...\n');
    
    const resumesSnapshot = await db.collection('resumes').get();
    const userResumeMap = new Map();
    
    // Group resumes by userId
    resumesSnapshot.docs.forEach(doc => {
      const resumeData = doc.data();
      const userId = resumeData.userId;
      
      if (userId) {
        if (!userResumeMap.has(userId)) {
          userResumeMap.set(userId, []);
        }
        userResumeMap.get(userId).push(doc.id);
      }
    });
    
    // Update user documents
    const batch = db.batch();
    let updatedUsers = 0;
    
    for (const [userId, resumeIds] of userResumeMap) {
      const userRef = db.collection('users').doc(userId);
      const userDoc = await userRef.get();
      
      if (userDoc.exists) {
        batch.update(userRef, {
          resumeIds: resumeIds,
          lastSignIn: new Date().toISOString()
        });
        console.log(`✅ Updated user ${userId} with ${resumeIds.length} resumes`);
        updatedUsers++;
      } else {
        console.log(`❌ User ${userId} not found, skipping...`);
      }
    }
    
    if (updatedUsers > 0) {
      await batch.commit();
      console.log(`\n🎉 Fixed ${updatedUsers} user documents`);
    } else {
      console.log('\n📭 No users to update');
    }
    
  } catch (error) {
    console.error('❌ Fix failed:', error.message);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    await verifyUserResumeLinks();
    
    const shouldFix = process.argv.includes('--fix');
    if (shouldFix) {
      console.log('\n' + '='.repeat(50));
      await fixUserResumeLinks();
    } else {
      console.log('\n💡 To fix any issues found, run:');
      console.log('   node verify_user_resume_links.js --fix');
    }
    
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

main()
  .then(() => {
    console.log('\n✅ Script completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
