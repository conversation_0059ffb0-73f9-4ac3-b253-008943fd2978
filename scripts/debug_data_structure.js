// <PERSON>ript to debug the exact data structure returned by Firebase
// Run this script with: node scripts/debug_data_structure.js

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
let serviceAccount;
try {
  serviceAccount = require('./serviceAccountKey.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: serviceAccount.project_id
  });
  console.log('✅ Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Firebase Admin SDK');
  console.error('   Make sure serviceAccountKey.json exists and is valid');
  process.exit(1);
}

const db = admin.firestore();
const TEST_USER_ID = 'Bbb0ezu0zeQqCDHghn3LLGXceM93';

async function debugDataStructure() {
  try {
    console.log('🔍 Debugging data structure...\n');
    
    // Get resume exactly like Flutter app does
    const resumesSnapshot = await db
      .collection('resumes')
      .where('userId', '==', TEST_USER_ID)
      .get();
    
    if (resumesSnapshot.empty) {
      console.log('❌ No resumes found');
      return;
    }
    
    const resumeDoc = resumesSnapshot.docs[0];
    const resumeId = resumeDoc.id;
    const mainData = resumeDoc.data();
    
    console.log('📋 Main Resume Document:');
    console.log(JSON.stringify(mainData, null, 2));
    
    console.log('\n📁 Subcollections:');
    
    // Get subcollections exactly like Flutter app does
    const subcollections = [
      'workExperience',
      'education',
      'projects', 
      'skills',
      'languages',
      'certifications',
      'socialMedia'
    ];
    
    const subcollectionData = {};
    
    for (const collectionName of subcollections) {
      const snapshot = await db
        .collection('resumes')
        .doc(resumeId)
        .collection(collectionName)
        .get();
      
      const data = snapshot.docs.map(doc => doc.data());
      subcollectionData[collectionName] = data;
      
      console.log(`\n${collectionName} (${data.length} items):`);
      if (data.length > 0) {
        console.log(JSON.stringify(data[0], null, 2)); // Show first item structure
        if (data.length > 1) {
          console.log(`... and ${data.length - 1} more items`);
        }
      }
    }
    
    // Simulate what Flutter app receives
    console.log('\n🔄 Simulating Flutter app data structure...');
    
    const completeResumeData = {
      ...mainData,
      ...subcollectionData
    };
    
    console.log('\n📱 Complete Resume Data Structure:');
    console.log('Keys:', Object.keys(completeResumeData));
    
    // Check for potential issues
    console.log('\n🔍 Checking for potential issues...');
    
    // Check if personalInfo is properly structured
    if (completeResumeData.personalInfo) {
      console.log('✅ personalInfo exists');
      console.log('personalInfo keys:', Object.keys(completeResumeData.personalInfo));
    } else {
      console.log('❌ personalInfo missing');
    }
    
    // Check if dates are properly formatted
    if (completeResumeData.createdAt) {
      console.log('✅ createdAt exists:', completeResumeData.createdAt);
      console.log('createdAt type:', typeof completeResumeData.createdAt);
    } else {
      console.log('❌ createdAt missing');
    }
    
    if (completeResumeData.updatedAt) {
      console.log('✅ updatedAt exists:', completeResumeData.updatedAt);
      console.log('updatedAt type:', typeof completeResumeData.updatedAt);
    } else {
      console.log('❌ updatedAt missing');
    }
    
    // Check subcollection data types
    for (const collectionName of subcollections) {
      const data = completeResumeData[collectionName];
      if (Array.isArray(data)) {
        console.log(`✅ ${collectionName} is array with ${data.length} items`);
        if (data.length > 0 && data[0].id) {
          console.log(`✅ ${collectionName} items have id field`);
        } else if (data.length > 0) {
          console.log(`❌ ${collectionName} items missing id field`);
        }
      } else {
        console.log(`❌ ${collectionName} is not an array:`, typeof data);
      }
    }
    
    // Check for Flutter model compatibility
    console.log('\n🎯 Flutter Model Compatibility Check:');
    
    const requiredFields = ['id', 'personalInfo', 'summary', 'createdAt', 'updatedAt'];
    const missingFields = requiredFields.filter(field => !(field in completeResumeData));
    
    if (missingFields.length === 0) {
      console.log('✅ All required fields present');
    } else {
      console.log('❌ Missing required fields:', missingFields);
    }
    
    // Check if we can create a minimal JSON that Flutter would accept
    console.log('\n📝 Minimal Flutter-compatible JSON:');
    const minimalData = {
      id: completeResumeData.id || 'missing',
      personalInfo: completeResumeData.personalInfo || {},
      summary: completeResumeData.summary || '',
      workExperience: completeResumeData.workExperience || [],
      education: completeResumeData.education || [],
      projects: completeResumeData.projects || [],
      skills: completeResumeData.skills || [],
      languages: completeResumeData.languages || [],
      certifications: completeResumeData.certifications || [],
      socialMedia: completeResumeData.socialMedia || [],
      createdAt: completeResumeData.createdAt || new Date().toISOString(),
      updatedAt: completeResumeData.updatedAt || new Date().toISOString()
    };
    
    console.log('Minimal data keys:', Object.keys(minimalData));
    console.log('All arrays?', Object.keys(minimalData).filter(key => 
      ['workExperience', 'education', 'projects', 'skills', 'languages', 'certifications', 'socialMedia'].includes(key)
    ).every(key => Array.isArray(minimalData[key])));
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error('Stack:', error.stack);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    await debugDataStructure();
    
    console.log('\n💡 If Flutter app still not showing data:');
    console.log('   1. Check Flutter console for error messages');
    console.log('   2. Verify model fromJson() methods handle the data structure');
    console.log('   3. Check if authentication is working properly');
    console.log('   4. Verify the test user ID is being used correctly');
    
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

main()
  .then(() => {
    console.log('\n✅ Debug completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
