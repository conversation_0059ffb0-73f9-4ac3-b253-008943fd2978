// <PERSON><PERSON>t to test if the uploaded data is accessible
// Run this script with: node scripts/test_data_access.js

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
let serviceAccount;
try {
  serviceAccount = require('./serviceAccountKey.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: serviceAccount.project_id
  });
  console.log('✅ Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Firebase Admin SDK');
  console.error('   Make sure serviceAccountKey.json exists and is valid');
  process.exit(1);
}

const db = admin.firestore();
const TEST_USER_ID = 'Bbb0ezu0zeQqCDHghn3LLGXceM93';

async function testDataAccess() {
  try {
    console.log('🔍 Testing data access...\n');
    
    // Test 1: Check if user document exists
    console.log('1. Checking user document...');
    const userDoc = await db.collection('users').doc(TEST_USER_ID).get();
    
    if (userDoc.exists) {
      const userData = userDoc.data();
      console.log(`   ✅ User found: ${userData.displayName} (${userData.email})`);
      console.log(`   📋 Resume IDs: ${userData.resumeIds?.length || 0}`);
      
      if (userData.resumeIds && userData.resumeIds.length > 0) {
        console.log(`   📝 Resume IDs: ${userData.resumeIds.join(', ')}`);
      }
    } else {
      console.log('   ❌ User document not found');
      return;
    }
    
    // Test 2: Check resumes collection
    console.log('\n2. Checking resumes collection...');
    const resumesSnapshot = await db
      .collection('resumes')
      .where('userId', '==', TEST_USER_ID)
      .get();
    
    console.log(`   📋 Found ${resumesSnapshot.docs.length} resumes`);
    
    if (resumesSnapshot.docs.length === 0) {
      console.log('   ❌ No resumes found for this user');
      return;
    }
    
    // Test 3: Check each resume and its subcollections
    for (const doc of resumesSnapshot.docs) {
      const resumeData = doc.data();
      const resumeId = doc.id;
      
      console.log(`\n3. Checking resume: ${resumeId}`);
      console.log(`   👤 Name: ${resumeData.personalInfo?.firstName || 'Unknown'} ${resumeData.personalInfo?.lastName || 'User'}`);
      console.log(`   📧 Email: ${resumeData.personalInfo?.email || 'No email'}`);
      console.log(`   📝 Summary: ${resumeData.summary ? 'Yes' : 'No'}`);
      console.log(`   📅 Created: ${resumeData.createdAt}`);
      console.log(`   📅 Updated: ${resumeData.updatedAt}`);
      
      // Check subcollections
      const subcollections = [
        'workExperience',
        'education',
        'projects',
        'skills',
        'languages',
        'certifications',
        'socialMedia'
      ];
      
      console.log('   📁 Subcollections:');
      for (const collectionName of subcollections) {
        const subcollectionSnapshot = await db
          .collection('resumes')
          .doc(resumeId)
          .collection(collectionName)
          .get();
        
        console.log(`     - ${collectionName}: ${subcollectionSnapshot.docs.length} items`);
      }
    }
    
    // Test 4: Simulate Flutter app data access
    console.log('\n4. Simulating Flutter app data access...');
    
    // This mimics what the Flutter app does (without orderBy to avoid index requirement)
    const querySnapshot = await db
      .collection('resumes')
      .where('userId', '==', TEST_USER_ID)
      .get();
    
    console.log(`   📱 App would see ${querySnapshot.docs.length} resumes`);
    
    for (const doc of querySnapshot.docs) {
      const data = doc.data();
      console.log(`     - ${data.personalInfo?.firstName || 'Unknown'} ${data.personalInfo?.lastName || 'User'} (${doc.id})`);
    }
    
    // Test 5: Check if data structure is correct for Flutter
    if (querySnapshot.docs.length > 0) {
      const firstResume = querySnapshot.docs[0];
      const resumeData = firstResume.data();
      
      console.log('\n5. Checking data structure compatibility...');
      
      const requiredFields = ['id', 'userId', 'personalInfo', 'summary', 'createdAt', 'updatedAt'];
      const missingFields = requiredFields.filter(field => !(field in resumeData));
      
      if (missingFields.length === 0) {
        console.log('   ✅ All required fields present');
      } else {
        console.log(`   ❌ Missing fields: ${missingFields.join(', ')}`);
      }
      
      // Check personalInfo structure
      if (resumeData.personalInfo) {
        const personalInfoFields = ['firstName', 'lastName', 'email'];
        const missingPersonalFields = personalInfoFields.filter(field => !(field in resumeData.personalInfo));
        
        if (missingPersonalFields.length === 0) {
          console.log('   ✅ Personal info structure correct');
        } else {
          console.log(`   ❌ Missing personal info fields: ${missingPersonalFields.join(', ')}`);
        }
      }
    }
    
    console.log('\n🎉 Data access test completed!');
    console.log('\n📱 Your Flutter app should now be able to:');
    console.log('   • Load the test user\'s resumes');
    console.log('   • Display resume list');
    console.log('   • Open individual resumes');
    console.log('   • Access all subcollection data');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    await testDataAccess();
    
    console.log('\n💡 Next steps:');
    console.log('   1. Run your Flutter app');
    console.log('   2. Navigate to "My Resumes" page');
    console.log('   3. You should see the test resumes');
    console.log('   4. Try opening a resume to test subcollection loading');
    
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

main()
  .then(() => {
    console.log('\n✅ Script completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
