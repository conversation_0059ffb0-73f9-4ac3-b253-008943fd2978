// <PERSON>ript to test adding new items to existing resume
// Run this script with: node scripts/test_add_functionality.js

const admin = require('firebase-admin');
const { v4: uuidv4 } = require('uuid');

// Initialize Firebase Admin SDK
let serviceAccount;
try {
  serviceAccount = require('./serviceAccountKey.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: serviceAccount.project_id
  });
  console.log('✅ Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Firebase Admin SDK');
  console.error('   Make sure serviceAccountKey.json exists and is valid');
  process.exit(1);
}

const db = admin.firestore();
const TEST_USER_ID = 'Bbb0ezu0zeQqCDHghn3LLGXceM93';

async function addNewItemsToResume() {
  try {
    console.log('🔍 Finding existing resume...\n');
    
    // Get existing resume
    const resumesSnapshot = await db
      .collection('resumes')
      .where('userId', '==', TEST_USER_ID)
      .limit(1)
      .get();
    
    if (resumesSnapshot.empty) {
      console.log('❌ No existing resume found. Please run upload-test-data first.');
      return;
    }
    
    const resumeDoc = resumesSnapshot.docs[0];
    const resumeId = resumeDoc.id;
    const resumeData = resumeDoc.data();
    
    console.log(`📋 Found resume: ${resumeData.personalInfo?.firstName} ${resumeData.personalInfo?.lastName}`);
    console.log(`📝 Resume ID: ${resumeId}\n`);
    
    // Add new work experience
    console.log('💼 Adding new work experience...');
    const newWorkExp = {
      id: uuidv4(),
      jobTitle: 'Flutter Developer',
      company: 'Tech Startup Inc.',
      location: 'Remote',
      startDate: new Date('2024-01-01').toISOString(),
      endDate: null,
      isCurrentJob: true,
      description: 'Developing mobile applications using Flutter and Dart',
      achievements: [
        'Built cross-platform mobile app with 10k+ downloads',
        'Implemented real-time chat functionality',
        'Optimized app performance by 40%'
      ]
    };
    
    await db.collection('resumes').doc(resumeId).collection('workExperience').doc(newWorkExp.id).set(newWorkExp);
    console.log('   ✅ Added new work experience');
    
    // Add new education
    console.log('🎓 Adding new education...');
    const newEducation = {
      id: uuidv4(),
      degree: 'Certificate in Mobile App Development',
      institution: 'Online Tech Academy',
      location: 'Online',
      startDate: new Date('2023-06-01').toISOString(),
      endDate: new Date('2023-12-01').toISOString(),
      isCurrentlyStudying: false,
      gpa: null,
      description: 'Intensive 6-month program focused on Flutter development'
    };
    
    await db.collection('resumes').doc(resumeId).collection('education').doc(newEducation.id).set(newEducation);
    console.log('   ✅ Added new education');
    
    // Add new project
    console.log('🚀 Adding new project...');
    const newProject = {
      id: uuidv4(),
      name: 'Resume Builder App',
      description: 'A Flutter application for creating and managing professional resumes with Firebase backend',
      technologies: ['Flutter', 'Dart', 'Firebase', 'Firestore', 'BLoC'],
      startDate: new Date('2024-08-01').toISOString(),
      endDate: new Date('2024-12-01').toISOString(),
      projectUrl: 'https://resume-builder.example.com',
      githubUrl: 'https://github.com/user/resume-builder'
    };
    
    await db.collection('resumes').doc(resumeId).collection('projects').doc(newProject.id).set(newProject);
    console.log('   ✅ Added new project');
    
    // Add new skill category
    console.log('⭐ Adding new skill category...');
    const newSkillCategory = {
      id: uuidv4(),
      category: 'Mobile Development',
      skills: [
        { id: uuidv4(), name: 'Flutter', level: 'Advanced' },
        { id: uuidv4(), name: 'React Native', level: 'Intermediate' },
        { id: uuidv4(), name: 'iOS Development', level: 'Beginner' },
        { id: uuidv4(), name: 'Android Development', level: 'Intermediate' }
      ]
    };
    
    await db.collection('resumes').doc(resumeId).collection('skills').doc(newSkillCategory.id).set(newSkillCategory);
    console.log('   ✅ Added new skill category');
    
    // Add new language
    console.log('🌍 Adding new language...');
    const newLanguage = {
      id: uuidv4(),
      language: 'German',
      proficiency: 'Intermediate'
    };
    
    await db.collection('resumes').doc(resumeId).collection('languages').doc(newLanguage.id).set(newLanguage);
    console.log('   ✅ Added new language');
    
    // Add new certification
    console.log('🏆 Adding new certification...');
    const newCertification = {
      id: uuidv4(),
      name: 'Flutter Certified Developer',
      issuer: 'Google',
      issueDate: new Date('2024-06-01').toISOString(),
      expiryDate: new Date('2026-06-01').toISOString(),
      credentialId: 'FLUTTER-2024-001',
      credentialUrl: 'https://developers.google.com/certification/flutter'
    };
    
    await db.collection('resumes').doc(resumeId).collection('certifications').doc(newCertification.id).set(newCertification);
    console.log('   ✅ Added new certification');
    
    // Update main resume document timestamp
    console.log('📝 Updating resume timestamp...');
    await db.collection('resumes').doc(resumeId).update({
      updatedAt: new Date().toISOString()
    });
    console.log('   ✅ Updated resume timestamp');
    
    console.log('\n🎉 Successfully added new items to all sections!');
    console.log('\n📱 Test in your Flutter app:');
    console.log('   1. Open the resume in the app');
    console.log('   2. Navigate through each section');
    console.log('   3. Verify all new items appear');
    console.log('   4. Try adding more items through the UI');
    console.log('   5. Check that they sync to Firebase');
    
    // Display summary
    console.log('\n📊 Added items summary:');
    console.log('   💼 Work Experience: Flutter Developer at Tech Startup Inc.');
    console.log('   🎓 Education: Certificate in Mobile App Development');
    console.log('   🚀 Project: Resume Builder App');
    console.log('   ⭐ Skills: Mobile Development category');
    console.log('   🌍 Language: German (Intermediate)');
    console.log('   🏆 Certification: Flutter Certified Developer');
    
  } catch (error) {
    console.error('❌ Failed to add new items:', error.message);
    throw error;
  }
}

async function verifyNewItems() {
  try {
    console.log('\n🔍 Verifying new items were added...\n');
    
    const resumesSnapshot = await db
      .collection('resumes')
      .where('userId', '==', TEST_USER_ID)
      .limit(1)
      .get();
    
    if (resumesSnapshot.empty) {
      console.log('❌ No resume found');
      return;
    }
    
    const resumeId = resumesSnapshot.docs[0].id;
    
    // Check each subcollection
    const subcollections = [
      'workExperience',
      'education', 
      'projects',
      'skills',
      'languages',
      'certifications'
    ];
    
    for (const collectionName of subcollections) {
      const snapshot = await db
        .collection('resumes')
        .doc(resumeId)
        .collection(collectionName)
        .get();
      
      console.log(`   📁 ${collectionName}: ${snapshot.docs.length} items`);
    }
    
    console.log('\n✅ Verification complete!');
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
  }
}

// Main execution
async function main() {
  try {
    await addNewItemsToResume();
    await verifyNewItems();
    
    console.log('\n💡 Next steps:');
    console.log('   1. Run your Flutter app');
    console.log('   2. Open the existing resume');
    console.log('   3. Check each section for new items');
    console.log('   4. Test adding more items through the UI');
    console.log('   5. Verify they appear in Firebase Console');
    
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

main()
  .then(() => {
    console.log('\n✅ Script completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
