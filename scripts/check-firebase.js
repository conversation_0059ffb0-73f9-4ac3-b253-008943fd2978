// Pre-flight check script to verify Firebase setup
const admin = require('firebase-admin');
const fs = require('fs');

// Initialize Firebase Admin SDK
let serviceAccount;
try {
  serviceAccount = require('./serviceAccountKey.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: serviceAccount.project_id
  });
  console.log('✅ Firebase Admin SDK initialized successfully');
  console.log(`   Project ID: ${serviceAccount.project_id}`);
} catch (error) {
  console.error('❌ Failed to initialize Firebase Admin SDK');
  console.error('   Make sure serviceAccountKey.json exists and is valid');
  process.exit(1);
}

const db = admin.firestore();

async function checkFirebaseSetup() {
  console.log('\n🔍 Checking Firebase setup...\n');
  
  const checks = [
    checkFirestoreAccess,
    checkFirestoreRules,
    checkProjectConfiguration
  ];
  
  let allPassed = true;
  
  for (const check of checks) {
    try {
      await check();
    } catch (error) {
      allPassed = false;
      console.error(`❌ ${error.message}`);
    }
  }
  
  if (allPassed) {
    console.log('\n🎉 All checks passed! Your Firebase setup is ready.');
    console.log('\n🚀 You can now run:');
    console.log('   npm run upload-test-data');
    console.log('   npm run upload-multiple');
  } else {
    console.log('\n⚠️  Some checks failed. Please fix the issues above before uploading data.');
  }
  
  return allPassed;
}

async function checkFirestoreAccess() {
  console.log('1. Testing Firestore access...');
  
  try {
    // Try to read from Firestore (this will fail if API is not enabled)
    const testCollection = db.collection('_test_connection');
    await testCollection.limit(1).get();
    console.log('   ✅ Firestore API is enabled and accessible');
  } catch (error) {
    if (error.message.includes('Cloud Firestore API has not been used')) {
      throw new Error(`Firestore API not enabled. 
   
   🔧 To fix:
   1. Go to Firebase Console: https://console.firebase.google.com/
   2. Select your project: ${serviceAccount.project_id}
   3. Go to "Firestore Database" in the sidebar
   4. Click "Create database"
   5. Choose "Start in test mode" (for development)
   6. Select a location and create the database
   7. Wait a few minutes for the API to be enabled`);
    } else {
      throw new Error(`Firestore access failed: ${error.message}`);
    }
  }
}

async function checkFirestoreRules() {
  console.log('2. Checking Firestore security rules...');
  
  try {
    // Try to write a test document
    const testDoc = db.collection('_test_write').doc('test');
    await testDoc.set({ test: true, timestamp: new Date() });
    await testDoc.delete(); // Clean up
    console.log('   ✅ Write permissions are working');
  } catch (error) {
    if (error.message.includes('PERMISSION_DENIED')) {
      console.log('   ⚠️  Write permissions may be restricted');
      console.log('   📝 Make sure your Firestore rules allow writes for service accounts');
      console.log('   💡 For testing, you can use these rules:');
      console.log(`
   rules_version = '2';
   service cloud.firestore {
     match /databases/{database}/documents {
       match /{document=**} {
         allow read, write: if true; // WARNING: Only for testing!
       }
     }
   }`);
    } else {
      throw new Error(`Firestore rules check failed: ${error.message}`);
    }
  }
}

async function checkProjectConfiguration() {
  console.log('3. Checking project configuration...');
  
  // Check if project ID is still the default
  if (serviceAccount.project_id === 'your-project-id') {
    throw new Error(`Project ID is still set to default 'your-project-id'.
   
   🔧 To fix:
   1. Make sure you downloaded the correct serviceAccountKey.json
   2. Or run: npm run setup`);
  }
  
  // Check if service account has required permissions
  const requiredRoles = [
    'Cloud Datastore User',
    'Firebase Admin SDK Administrator Service Agent'
  ];
  
  console.log('   ✅ Project ID is configured correctly');
  console.log(`   📋 Service account: ${serviceAccount.client_email}`);
  console.log('   💡 Make sure the service account has Firestore permissions');
}

// Run the checks
checkFirebaseSetup()
  .then((success) => {
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('\n❌ Setup check failed:', error.message);
    process.exit(1);
  });
