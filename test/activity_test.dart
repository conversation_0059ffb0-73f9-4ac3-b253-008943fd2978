import 'package:flutter_test/flutter_test.dart';
import 'package:resume_builder/data/models/activity_model.dart';

void main() {
  group('Activity Model Tests', () {
    test('ActivityFactory should create proper activities', () {
      // Arrange
      const id = 'test-id';
      const resumeId = 'resume-id';
      const resumeName = 'Test Resume';

      // Act
      final activity = ActivityFactory.createResumeActivity(
        id: id,
        type: ActivityType.resumeCreated,
        resumeId: resumeId,
        resumeName: resumeName,
      );

      // Assert
      expect(activity.id, id);
      expect(activity.type, ActivityType.resumeCreated);
      expect(activity.resumeId, resumeId);
      expect(activity.resumeName, resumeName);
      expect(activity.title, 'Resume Created');
      expect(activity.description, 'Created new resume "$resumeName"');
    });

    test('ActivityFactory should create section edited activity', () {
      // Arrange
      const id = 'test-id';
      const resumeId = 'resume-id';
      const resumeName = 'Test Resume';
      const sectionName = 'Personal Information';

      // Act
      final activity = ActivityFactory.createResumeActivity(
        id: id,
        type: ActivityType.sectionEdited,
        resumeId: resumeId,
        resumeName: resumeName,
        sectionName: sectionName,
      );

      // Assert
      expect(activity.id, id);
      expect(activity.type, ActivityType.sectionEdited);
      expect(activity.resumeId, resumeId);
      expect(activity.resumeName, resumeName);
      expect(activity.sectionName, sectionName);
      expect(activity.title, 'Section Edited');
      expect(activity.description, 'Edited $sectionName section in "$resumeName"');
    });

    test('ActivityModel should serialize to and from JSON', () {
      // Arrange
      final originalActivity = ActivityModel(
        id: 'test-id',
        type: ActivityType.resumeUpdated,
        title: 'Test Title',
        description: 'Test Description',
        timestamp: DateTime(2024, 1, 1, 12, 0, 0),
        resumeId: 'resume-id',
        resumeName: 'Test Resume',
        sectionName: 'Test Section',
        metadata: {'key': 'value'},
      );

      // Act
      final json = originalActivity.toJson();
      final deserializedActivity = ActivityModel.fromJson(json);

      // Assert
      expect(deserializedActivity.id, originalActivity.id);
      expect(deserializedActivity.type, originalActivity.type);
      expect(deserializedActivity.title, originalActivity.title);
      expect(deserializedActivity.description, originalActivity.description);
      expect(deserializedActivity.timestamp, originalActivity.timestamp);
      expect(deserializedActivity.resumeId, originalActivity.resumeId);
      expect(deserializedActivity.resumeName, originalActivity.resumeName);
      expect(deserializedActivity.sectionName, originalActivity.sectionName);
      expect(deserializedActivity.metadata, originalActivity.metadata);
    });

    test('ActivityModel copyWith should work correctly', () {
      // Arrange
      final originalActivity = ActivityModel(
        id: 'test-id',
        type: ActivityType.resumeCreated,
        title: 'Original Title',
        description: 'Original Description',
        timestamp: DateTime(2024, 1, 1),
        resumeId: 'resume-id',
        resumeName: 'Original Resume',
      );

      // Act
      final copiedActivity = originalActivity.copyWith(
        title: 'New Title',
        description: 'New Description',
      );

      // Assert
      expect(copiedActivity.id, originalActivity.id);
      expect(copiedActivity.type, originalActivity.type);
      expect(copiedActivity.title, 'New Title');
      expect(copiedActivity.description, 'New Description');
      expect(copiedActivity.timestamp, originalActivity.timestamp);
      expect(copiedActivity.resumeId, originalActivity.resumeId);
      expect(copiedActivity.resumeName, originalActivity.resumeName);
    });

    test('ActivityType enum should have all expected values', () {
      // Assert
      expect(ActivityType.values.length, 8);
      expect(ActivityType.values.contains(ActivityType.resumeCreated), true);
      expect(ActivityType.values.contains(ActivityType.resumeUpdated), true);
      expect(ActivityType.values.contains(ActivityType.resumeSaved), true);
      expect(ActivityType.values.contains(ActivityType.resumeExported), true);
      expect(ActivityType.values.contains(ActivityType.sectionEdited), true);
      expect(ActivityType.values.contains(ActivityType.templateUsed), true);
      expect(ActivityType.values.contains(ActivityType.resumeViewed), true);
      expect(ActivityType.values.contains(ActivityType.resumeDeleted), true);
    });

    test('ActivityFactory should create different activity types correctly', () {
      const id = 'test-id';
      const resumeId = 'resume-id';
      const resumeName = 'Test Resume';

      // Test all activity types
      final activities = [
        ActivityFactory.createResumeActivity(
          id: id,
          type: ActivityType.resumeCreated,
          resumeId: resumeId,
          resumeName: resumeName,
        ),
        ActivityFactory.createResumeActivity(
          id: id,
          type: ActivityType.resumeUpdated,
          resumeId: resumeId,
          resumeName: resumeName,
        ),
        ActivityFactory.createResumeActivity(
          id: id,
          type: ActivityType.resumeSaved,
          resumeId: resumeId,
          resumeName: resumeName,
        ),
        ActivityFactory.createResumeActivity(
          id: id,
          type: ActivityType.resumeExported,
          resumeId: resumeId,
          resumeName: resumeName,
        ),
        ActivityFactory.createResumeActivity(
          id: id,
          type: ActivityType.templateUsed,
          resumeId: resumeId,
          resumeName: resumeName,
        ),
        ActivityFactory.createResumeActivity(
          id: id,
          type: ActivityType.resumeViewed,
          resumeId: resumeId,
          resumeName: resumeName,
        ),
        ActivityFactory.createResumeActivity(
          id: id,
          type: ActivityType.resumeDeleted,
          resumeId: resumeId,
          resumeName: resumeName,
        ),
      ];

      // Assert each activity has the correct type and non-empty title/description
      for (final activity in activities) {
        expect(activity.title.isNotEmpty, true);
        expect(activity.description.isNotEmpty, true);
        expect(activity.resumeId, resumeId);
        expect(activity.resumeName, resumeName);
      }
    });
  });
}
