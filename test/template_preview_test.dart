import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:resume_builder/data/models/resume_template_model.dart';
import 'package:resume_builder/data/models/simple_resume_model.dart';
import 'package:resume_builder/presentation/cubits/template/template_cubit.dart';
import 'package:resume_builder/presentation/widgets/templates/template_preview_widget.dart';
import 'package:resume_builder/presentation/widgets/templates/template_preview_dialog.dart';

void main() {
  group('Template Preview Tests', () {
    late ResumeTemplateModel testTemplate;
    late ResumeModel testResume;

    setUp(() {
      testTemplate = TemplateRepository.getAllTemplates().first;
      testResume = TemplateRepository.getSampleResumeData();
    });

    testWidgets('TemplatePreviewWidget renders correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TemplatePreviewWidget(
              resume: testResume,
              template: testTemplate,
            ),
          ),
        ),
      );

      // Verify the widget renders without errors
      expect(find.byType(TemplatePreviewWidget), findsOneWidget);
      
      // Verify sample data is displayed
      expect(find.text('Sarah <PERSON>'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
    });

    testWidgets('CompactTemplatePreview renders correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CompactTemplatePreview(
              template: testTemplate,
              sampleResume: testResume,
            ),
          ),
        ),
      );

      // Verify the widget renders without errors
      expect(find.byType(CompactTemplatePreview), findsOneWidget);
    });

    testWidgets('TemplatePreviewDialog shows template information', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider(
            create: (context) => TemplateCubit(),
            child: Scaffold(
              body: Builder(
                builder: (context) => ElevatedButton(
                  onPressed: () => showTemplatePreview(context, testTemplate),
                  child: const Text('Show Preview'),
                ),
              ),
            ),
          ),
        ),
      );

      // Tap the button to show the preview
      await tester.tap(find.text('Show Preview'));
      await tester.pumpAndSettle();

      // Verify the dialog is shown
      expect(find.byType(TemplatePreviewDialog), findsOneWidget);
      
      // Verify template information is displayed
      expect(find.text(testTemplate.name), findsOneWidget);
      expect(find.text(testTemplate.description), findsOneWidget);
      expect(find.text('Select Template'), findsWidgets);
    });

    test('Sample resume data is properly structured', () {
      final sampleResume = TemplateRepository.getSampleResumeData();
      
      expect(sampleResume.personalInfo.firstName, 'Sarah');
      expect(sampleResume.personalInfo.lastName, 'Johnson');
      expect(sampleResume.summary.isNotEmpty, true);
      expect(sampleResume.workExperience.isNotEmpty, true);
      expect(sampleResume.education.isNotEmpty, true);
      expect(sampleResume.skills.isNotEmpty, true);
      expect(sampleResume.projects.isNotEmpty, true);
    });

    test('All templates have required properties', () {
      final templates = TemplateRepository.getAllTemplates();
      
      for (final template in templates) {
        expect(template.id.isNotEmpty, true);
        expect(template.name.isNotEmpty, true);
        expect(template.description.isNotEmpty, true);
        expect(template.style, isNotNull);
        expect(template.category, isNotNull);
      }
    });
  });
}
