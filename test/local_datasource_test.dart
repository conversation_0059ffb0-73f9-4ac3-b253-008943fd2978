import 'package:flutter_test/flutter_test.dart';
import 'package:hive/hive.dart';
import 'package:resume_builder/data/datasources/local_resume_datasource.dart';
import 'package:resume_builder/data/datasources/local_activity_datasource.dart';
import 'package:resume_builder/data/models/simple_resume_model.dart';
import 'package:resume_builder/data/models/activity_model.dart';
import 'package:resume_builder/core/constants/app_constants.dart';

void main() {
  group('Local DataSource Type Casting Tests', () {
    late LocalResumeDataSource resumeDataSource;
    late LocalActivityDataSource activityDataSource;

    setUpAll(() async {
      // Initialize Hive for testing
      Hive.init('./test/hive_test_db');
    });

    setUp(() {
      resumeDataSource = LocalResumeDataSource();
      activityDataSource = LocalActivityDataSource();
    });

    tearDown(() async {
      // Clean up test data after each test
      try {
        final resumeBox = await Hive.openBox(AppConstants.resumeBox);
        await resumeBox.clear();
        await resumeBox.close();
      } catch (e) {
        // Ignore cleanup errors
      }

      try {
        final activityBox = await Hive.openBox('activity');
        await activityBox.clear();
        await activityBox.close();
      } catch (e) {
        // Ignore cleanup errors
      }
    });

    group('LocalResumeDataSource Type Conversion Tests', () {
      test('should handle Map<dynamic, dynamic> to Map<String, dynamic> conversion in getResume', () async {
        // Arrange - Create a test resume
        final testResume = _createTestResume();
        
        // Save the resume first
        await resumeDataSource.saveResume(testResume);

        // Act - Retrieve the resume (this will test the type conversion)
        final retrievedResume = await resumeDataSource.getResume();

        // Assert
        expect(retrievedResume, isNotNull);
        expect(retrievedResume!.id, testResume.id);
        expect(retrievedResume.personalInfo.firstName, testResume.personalInfo.firstName);
        expect(retrievedResume.personalInfo.lastName, testResume.personalInfo.lastName);
        expect(retrievedResume.summary, testResume.summary);
        expect(retrievedResume.workExperience.length, testResume.workExperience.length);
        expect(retrievedResume.education.length, testResume.education.length);
      });

      test('should handle Map<dynamic, dynamic> to Map<String, dynamic> conversion in getAllResumes', () async {
        // Arrange - Create multiple test resumes
        final testResume1 = _createTestResume(id: 'resume-1', firstName: 'John');
        final testResume2 = _createTestResume(id: 'resume-2', firstName: 'Jane');
        
        // Save the resumes
        await resumeDataSource.saveResume(testResume1);
        await resumeDataSource.saveResume(testResume2);

        // Act - Retrieve all resumes (this will test the type conversion)
        final retrievedResumes = await resumeDataSource.getAllResumes();

        // Assert
        expect(retrievedResumes.length, 2);
        
        final resume1 = retrievedResumes.firstWhere((r) => r.id == 'resume-1');
        final resume2 = retrievedResumes.firstWhere((r) => r.id == 'resume-2');
        
        expect(resume1.personalInfo.firstName, 'John');
        expect(resume2.personalInfo.firstName, 'Jane');
      });

      test('should handle nested Map conversion in complex resume data', () async {
        // Arrange - Create a resume with complex nested data
        final testResume = _createComplexTestResume();
        
        // Save the resume
        await resumeDataSource.saveResume(testResume);

        // Act - Retrieve the resume
        final retrievedResume = await resumeDataSource.getResume();

        // Assert - Check nested data is properly converted
        expect(retrievedResume, isNotNull);
        expect(retrievedResume!.workExperience.isNotEmpty, true);
        expect(retrievedResume.workExperience.first.achievements.isNotEmpty, true);
        expect(retrievedResume.skills.isNotEmpty, true);
        expect(retrievedResume.skills.first.skills.isNotEmpty, true);
        expect(retrievedResume.projects.isNotEmpty, true);
        expect(retrievedResume.projects.first.technologies.isNotEmpty, true);
      });
    });

    group('LocalActivityDataSource Type Conversion Tests', () {
      test('should handle Map<dynamic, dynamic> to Map<String, dynamic> conversion in getAllActivities', () async {
        // Arrange - Create test activities
        final testActivity1 = _createTestActivity(id: 'activity-1', title: 'Test Activity 1');
        final testActivity2 = _createTestActivity(id: 'activity-2', title: 'Test Activity 2');
        
        // Save the activities
        await activityDataSource.saveActivity(testActivity1);
        await activityDataSource.saveActivity(testActivity2);

        // Act - Retrieve all activities (this will test the type conversion)
        final retrievedActivities = await activityDataSource.getAllActivities();

        // Assert
        expect(retrievedActivities.length, 2);
        
        final activity1 = retrievedActivities.firstWhere((a) => a.id == 'activity-1');
        final activity2 = retrievedActivities.firstWhere((a) => a.id == 'activity-2');
        
        expect(activity1.title, 'Test Activity 1');
        expect(activity2.title, 'Test Activity 2');
      });

      test('should handle activities with metadata Map conversion', () async {
        // Arrange - Create activity with metadata
        final testActivity = ActivityModel(
          id: 'test-activity',
          type: ActivityType.resumeUpdated,
          title: 'Test Activity',
          description: 'Test Description',
          timestamp: DateTime.now(),
          resumeId: 'resume-id',
          resumeName: 'Test Resume',
          metadata: {
            'section': 'personal_info',
            'changes': ['firstName', 'lastName'],
            'nested': {
              'key1': 'value1',
              'key2': 'value2',
            }
          },
        );
        
        // Save the activity
        await activityDataSource.saveActivity(testActivity);

        // Act - Retrieve the activity
        final retrievedActivities = await activityDataSource.getAllActivities();

        // Assert
        expect(retrievedActivities.length, 1);
        final retrievedActivity = retrievedActivities.first;
        expect(retrievedActivity.metadata, isNotNull);
        expect(retrievedActivity.metadata!['section'], 'personal_info');
        expect(retrievedActivity.metadata!['changes'], isA<List>());
        expect(retrievedActivity.metadata!['nested'], isA<Map>());
      });
    });

    group('Error Handling Tests', () {
      test('should handle empty data gracefully', () async {
        // Act & Assert - Should not throw when no data exists
        final resume = await resumeDataSource.getResume();
        final resumes = await resumeDataSource.getAllResumes();
        final activities = await activityDataSource.getAllActivities();

        expect(resume, isNull);
        expect(resumes, isEmpty);
        expect(activities, isEmpty);
      });
    });
  });
}

// Helper functions to create test data
ResumeModel _createTestResume({String? id, String? firstName}) {
  return ResumeModel(
    id: id ?? 'test-resume-id',
    personalInfo: PersonalInfoModel(
      firstName: firstName ?? 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
      address: '123 Main St',
      city: 'Anytown',
      state: 'CA',
      zipCode: '12345',
      country: 'USA',
    ),
    summary: 'Test summary',
    workExperience: [],
    education: [],
    projects: [],
    skills: [],
    languages: [],
    certifications: [],
    socialMedia: [],
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );
}

ResumeModel _createComplexTestResume() {
  return ResumeModel(
    id: 'complex-resume-id',
    personalInfo: PersonalInfoModel(
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '+1234567890',
      address: '456 Oak Ave',
      city: 'Somewhere',
      state: 'NY',
      zipCode: '67890',
      country: 'USA',
    ),
    summary: 'Complex test summary',
    workExperience: [
      WorkExperienceModel(
        id: 'work-1',
        jobTitle: 'Software Developer',
        company: 'Tech Corp',
        location: 'San Francisco, CA',
        startDate: DateTime(2020, 1, 1),
        endDate: DateTime(2023, 12, 31),
        isCurrentJob: false,
        description: 'Developed software applications',
        achievements: ['Achievement 1', 'Achievement 2'],
      ),
    ],
    education: [
      EducationModel(
        id: 'edu-1',
        degree: 'Bachelor of Science',
        institution: 'University of Technology',
        location: 'Tech City, CA',
        startDate: DateTime(2016, 9, 1),
        endDate: DateTime(2020, 5, 31),
        isCurrentlyStudying: false,
        gpa: '3.8',
        description: 'Computer Science major',
      ),
    ],
    projects: [
      ProjectModel(
        id: 'project-1',
        name: 'Test Project',
        description: 'A test project',
        technologies: ['Flutter', 'Dart', 'Firebase'],
        achievements: ['Built mobile app', 'Deployed to production'],
      ),
    ],
    skills: [
      SkillCategoryModel(
        id: 'skill-cat-1',
        category: 'Programming',
        skills: [
          SkillModel(
            id: 'skill-1',
            name: 'Flutter',
            proficiencyLevel: 5,
          ),
        ],
      ),
    ],
    languages: [],
    certifications: [],
    socialMedia: [],
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );
}

ActivityModel _createTestActivity({String? id, String? title}) {
  return ActivityModel(
    id: id ?? 'test-activity-id',
    type: ActivityType.resumeCreated,
    title: title ?? 'Test Activity',
    description: 'Test activity description',
    timestamp: DateTime.now(),
    resumeId: 'test-resume-id',
    resumeName: 'Test Resume',
  );
}
