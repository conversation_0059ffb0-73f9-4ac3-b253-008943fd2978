# 🔥 Firebase Setup Guide

## Current Issues from Logs:
1. ✅ **Firebase Auth**: Working correctly (user authenticated)
2. ❌ **Cloud Firestore**: API not enabled
3. ⚠️ **App Check**: Not configured (optional for development)

## 🚨 Critical: Enable Cloud Firestore

### Step 1: Enable Firestore API
1. **Click this link**: https://console.developers.google.com/apis/api/firestore.googleapis.com/overview?project=resume-d24cb
2. **Click "Enable"** button
3. **Wait 2-3 minutes** for the API to be enabled

### Step 2: Create Firestore Database
1. **Go to Firebase Console**: https://console.firebase.google.com/project/resume-d24cb
2. **Click "Firestore Database"** in the left sidebar
3. **Click "Create database"**
4. **Choose "Start in test mode"** (for development)
5. **Select location**: Choose closest to your users (e.g., us-central1)
6. **Click "Done"**

### Step 3: Configure Security Rules
1. In Firestore console, go to **"Rules"** tab
2. Replace the default rules with:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can only access their own resumes
    match /resumes/{resumeId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
```

3. **Click "Publish"**

## 🧪 Testing

After completing the setup:

1. **Run the app**: `flutter run --debug`
2. **Sign in with Google**
3. **Check debug console** for:
   ```
   ✅ Authenticated user: <EMAIL>
   🔥 Firestore connection test: users/your-uid
   ✅ Firestore write test successful
   ```

## 🔍 Expected Log Output

**Before Setup:**
```
W/Firestore: Stream closed with status: Status{code=PERMISSION_DENIED, description=Cloud Firestore API has not been used...}
```

**After Setup:**
```
D/FirebaseAuth: Notifying id token listeners about user
🔍 Checking Firestore Configuration...
✅ Authenticated user: <EMAIL>
✅ Firestore connection successful
✅ Firestore write test successful
```

## 🚨 If You Still Get Errors

### Error: "permission-denied"
- Check that security rules are published
- Ensure user is authenticated before Firestore operations

### Error: "unavailable"
- Wait 2-3 minutes after enabling the API
- Check your internet connection
- Verify the project ID is correct

### Error: "not-found"
- Make sure you created the database in the correct project
- Check that the project ID matches in your config files

## 📞 Need Help?

If you're still having issues:
1. Check the Firebase Console for any error messages
2. Verify your project ID: `resume-d24cb`
3. Make sure you're signed in to the correct Google account
4. Try refreshing the Firebase Console

## ✅ Success Checklist

- [ ] Cloud Firestore API enabled
- [ ] Firestore database created
- [ ] Security rules configured
- [ ] App runs without Firestore errors
- [ ] User can sign in and data is saved
