import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../cubits/auth/auth_cubit.dart';
import '../../cubits/resume/resume_cubit.dart';
import '../auth/auth_page.dart';
import '../resume_builder/resume_builder_page.dart';
import '../resumes/my_resumes_page.dart';
import '../profile/profile_page.dart';
import '../templates/templates_page.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/activity/recent_activity_widget.dart';
import '../activity/activity_page.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listener: (context, state) {
        if (state.errorMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errorMessage!),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      },
      child: Bloc<PERSON><PERSON>er<AuthCubit, AuthState>(
        builder: (context, authState) {
          if (authState.isLoading) {
            return const Scaffold(
              body: LoadingWidget(),
            );
          }

          if (!authState.isAuthenticated) {
            return const AuthPage();
          }

          return const AuthenticatedHomePage();
        },
      ),
    );
  }
}

class AuthenticatedHomePage extends StatefulWidget {
  const AuthenticatedHomePage({super.key});

  @override
  State<AuthenticatedHomePage> createState() => _AuthenticatedHomePageState();
}

class _AuthenticatedHomePageState extends State<AuthenticatedHomePage> {
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    // Load the last resume on app startup
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ResumeCubit>().loadLastResume();
    });
  }

  final List<Widget> _pages = [
    const DashboardPage(),
    const MyResumesPage(),
    const ResumeBuilderPage(),
    const TemplatesPage(),
    const ProfilePage(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ATS Resume Builder'),
        
      ),
      body: _pages[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items:  [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.folder),
            label: 'My Resumes',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.edit_document),
            label: 'Builder',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.file_present_outlined),
            label: 'Templates',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
    );
  }

}

class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Welcome back!',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _DashboardCard(
                  title: 'Create New Resume',
                  subtitle: 'Start building your resume',
                  icon: Icons.add_circle_outline,
                  onTap: () {
                    context.read<ResumeCubit>().createNewResume();
                    // Navigate to resume builder
                    Navigator.of(context).pushNamed('/resume-builder');
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _DashboardCard(
                  title: 'My Resumes',
                  subtitle: 'View and edit existing resumes',
                  icon: Icons.folder_outlined,
                  onTap: () {
                    // Navigate to resumes list
                    Navigator.of(context).pushNamed('/my-resumes');
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Card(
              child: RecentActivityWidget(
                maxItems: 5,
                showHeader: true,
                onViewAll: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const ActivityPage(),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _DashboardCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final VoidCallback onTap;

  const _DashboardCard({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                icon,
                size: 32,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
      ),
    );
  }
}




