import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../cubits/activity/activity_cubit.dart';
import '../../cubits/activity/activity_state.dart';
import '../../../data/models/activity_model.dart';
import '../../widgets/activity/activity_item_widget.dart';
import '../../widgets/activity/recent_activity_widget.dart';

class ActivityPage extends StatefulWidget {
  const ActivityPage({super.key});

  @override
  State<ActivityPage> createState() => _ActivityPageState();
}

class _ActivityPageState extends State<ActivityPage> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    
    // Load activities when page is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ActivityCubit>().loadAllActivities();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Activity History'),
        actions: [
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'clear_all',
                child: Row(
                  children: [
                    Icon(Icons.clear_all),
                    SizedBox(width: 8),
                    Text('Clear All'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'refresh',
                child: Row(
                  children: [
                    Icon(Icons.refresh),
                    SizedBox(width: 8),
                    Text('Refresh'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Today'),
            Tab(text: 'This Week'),
            Tab(text: 'Filters'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllActivitiesTab(),
          _buildTodayActivitiesTab(),
          _buildWeekActivitiesTab(),
          _buildFiltersTab(),
        ],
      ),
    );
  }

  Widget _buildAllActivitiesTab() {
    return BlocBuilder<ActivityCubit, ActivityState>(
      builder: (context, state) {
        return ActivityListWidget(
          activities: state.filteredActivities,
          isLoading: state.isLoading,
          errorMessage: state.errorMessage,
          onRefresh: () {
            context.read<ActivityCubit>().loadAllActivities();
          },
          onActivityTap: _handleActivityTap,
          onActivityDelete: _handleActivityDelete,
        );
      },
    );
  }

  Widget _buildTodayActivitiesTab() {
    return BlocBuilder<ActivityCubit, ActivityState>(
      builder: (context, state) {
        return Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Icon(
                    Icons.today,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Today\'s Activities',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () {
                      context.read<ActivityCubit>().loadTodaysActivities();
                    },
                    child: const Text('Refresh'),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ActivityListWidget(
                activities: state.filteredActivities,
                isLoading: state.isLoading,
                errorMessage: state.errorMessage,
                onRefresh: () {
                  context.read<ActivityCubit>().loadTodaysActivities();
                },
                onActivityTap: _handleActivityTap,
                onActivityDelete: _handleActivityDelete,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildWeekActivitiesTab() {
    return BlocBuilder<ActivityCubit, ActivityState>(
      builder: (context, state) {
        return Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Icon(
                    Icons.date_range,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'This Week\'s Activities',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () {
                      context.read<ActivityCubit>().loadThisWeeksActivities();
                    },
                    child: const Text('Refresh'),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ActivityListWidget(
                activities: state.filteredActivities,
                isLoading: state.isLoading,
                errorMessage: state.errorMessage,
                onRefresh: () {
                  context.read<ActivityCubit>().loadThisWeeksActivities();
                },
                onActivityTap: _handleActivityTap,
                onActivityDelete: _handleActivityDelete,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildFiltersTab() {
    return BlocBuilder<ActivityCubit, ActivityState>(
      builder: (context, state) {
        return SingleChildScrollView(
          child: Column(
            children: [
              ActivityFilterWidget(
                selectedType: state.selectedFilter,
                onTypeChanged: (type) {
                  context.read<ActivityCubit>().filterByType(type);
                },
                onClearFilters: () {
                  context.read<ActivityCubit>().clearFilters();
                },
              ),
              const Divider(),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Activity Statistics',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            _buildStatRow('Total Activities', state.activityCount.toString()),
                            const Divider(),
                            _buildStatRow('Filtered Results', state.filteredActivities.length.toString()),
                            const Divider(),
                            _buildStatRow('Selected Filter', _getFilterDisplayName(state.selectedFilter)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  String _getFilterDisplayName(ActivityType? type) {
    if (type == null) return 'None';
    
    switch (type) {
      case ActivityType.resumeCreated:
        return 'Created';
      case ActivityType.resumeUpdated:
        return 'Updated';
      case ActivityType.resumeSaved:
        return 'Saved';
      case ActivityType.resumeExported:
        return 'Exported';
      case ActivityType.sectionEdited:
        return 'Section Edited';
      case ActivityType.templateUsed:
        return 'Template Used';
      case ActivityType.resumeViewed:
        return 'Viewed';
      case ActivityType.resumeDeleted:
        return 'Deleted';
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'clear_all':
        _showClearAllDialog();
        break;
      case 'refresh':
        context.read<ActivityCubit>().loadAllActivities();
        break;
    }
  }

  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Activities'),
        content: const Text(
          'Are you sure you want to clear all activity history? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<ActivityCubit>().clearAllActivities();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  void _handleActivityTap(ActivityModel activity) {
    // Handle activity tap - navigate to relevant screen based on activity type
    if (activity.resumeId != null) {
      switch (activity.type) {
        case ActivityType.resumeCreated:
        case ActivityType.resumeUpdated:
        case ActivityType.resumeSaved:
        case ActivityType.sectionEdited:
        case ActivityType.resumeViewed:
          // Navigate to resume builder
          Navigator.of(context).pushNamed('/resume-builder');
          break;
        case ActivityType.resumeExported:
          // Show export info
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Resume "${activity.resumeName}" was exported'),
            ),
          );
          break;
        case ActivityType.templateUsed:
          // Could navigate to templates page
          break;
        case ActivityType.resumeDeleted:
          // Show info that resume was deleted
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Resume "${activity.resumeName}" was deleted'),
            ),
          );
          break;
      }
    }
  }

  void _handleActivityDelete(String activityId) {
    context.read<ActivityCubit>().deleteActivity(activityId);
  }
}
