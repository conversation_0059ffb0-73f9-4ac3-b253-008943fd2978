import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/models/user_model.dart';
import '../../../domain/usecases/auth_usecases.dart';

part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  final AuthUseCases _authUseCases;

  AuthCubit(this._authUseCases) : super(const AuthState());

  void checkAuthStatus() async {
    emit(state.copyWith(isLoading: true));
    
    try {
      final user = await _authUseCases.getCurrentUser();
      if (user != null) {
        emit(state.copyWith(
          isAuthenticated: true,
          user: user,
          isLoading: false,
          errorMessage: null,
        ));
      } else {
        emit(state.copyWith(
          isAuthenticated: false,
          user: null,
          isLoading: false,
          errorMessage: null,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        isAuthenticated: false,
        user: null,
        isLoading: false,
        errorMessage: e.toString(),
      ));
    }
  }

  void signInWithEmail(String email, String password) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    
    try {
      final user = await _authUseCases.signInWithEmail(email, password);
      emit(state.copyWith(
        isAuthenticated: true,
        user: user,
        isLoading: false,
        errorMessage: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        isAuthenticated: false,
        user: null,
        isLoading: false,
        errorMessage: e.toString(),
      ));
    }
  }

  void signUpWithEmail(String email, String password, String displayName) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    
    try {
      final user = await _authUseCases.signUpWithEmail(email, password, displayName);
      emit(state.copyWith(
        isAuthenticated: true,
        user: user,
        isLoading: false,
        errorMessage: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        isAuthenticated: false,
        user: null,
        isLoading: false,
        errorMessage: e.toString(),
      ));
    }
  }

  void signInWithGoogle() async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    
    try {
      final user = await _authUseCases.signInWithGoogle();
      emit(state.copyWith(
        isAuthenticated: true,
        user: user,
        isLoading: false,
        errorMessage: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        isAuthenticated: false,
        user: null,
        isLoading: false,
        errorMessage: e.toString(),
      ));
    }
  }

  void signInWithApple() async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    
    try {
      final user = await _authUseCases.signInWithApple();
      emit(state.copyWith(
        isAuthenticated: true,
        user: user,
        isLoading: false,
        errorMessage: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        isAuthenticated: false,
        user: null,
        isLoading: false,
        errorMessage: e.toString(),
      ));
    }
  }

  void signOut() async {
    emit(state.copyWith(isLoading: true));
    
    try {
      await _authUseCases.signOut();
      emit(state.copyWith(
        isAuthenticated: false,
        user: null,
        isLoading: false,
        errorMessage: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      ));
    }
  }

  void resetPassword(String email) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    
    try {
      await _authUseCases.resetPassword(email);
      emit(state.copyWith(
        isLoading: false,
        errorMessage: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      ));
    }
  }

  void clearError() {
    emit(state.copyWith(errorMessage: null));
  }
}
