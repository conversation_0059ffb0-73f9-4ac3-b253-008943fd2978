import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/usecases/activity_usecases.dart';
import '../../../data/models/activity_model.dart';
import 'activity_state.dart';

class ActivityCubit extends Cubit<ActivityState> {
  final ActivityUseCases _activityUseCases;

  ActivityCubit(this._activityUseCases) : super(const ActivityState());

  // Load recent activities
  Future<void> loadRecentActivities({int limit = 20}) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      final activities = await _activityUseCases.getRecentActivities(limit: limit);
      final count = await _activityUseCases.getActivityCount();
      
      emit(state.copyWith(
        activities: activities,
        filteredActivities: activities,
        isLoading: false,
        activityCount: count,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      ));
    }
  }

  // Load all activities
  Future<void> loadAllActivities() async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      final activities = await _activityUseCases.getAllActivities();
      final count = await _activityUseCases.getActivityCount();
      
      emit(state.copyWith(
        activities: activities,
        filteredActivities: activities,
        isLoading: false,
        activityCount: count,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      ));
    }
  }

  // Track a new activity
  Future<void> trackActivity({
    required ActivityType type,
    required String resumeId,
    required String resumeName,
    String? sectionName,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      await _activityUseCases.trackActivity(
        type: type,
        resumeId: resumeId,
        resumeName: resumeName,
        sectionName: sectionName,
        metadata: metadata,
      );
      
      // Reload activities to show the new one
      await loadRecentActivities();
    } catch (e) {
      emit(state.copyWith(errorMessage: e.toString()));
    }
  }

  // Filter activities by type
  void filterByType(ActivityType? type) {
    if (type == null) {
      // Clear filter
      emit(state.copyWith(
        filteredActivities: state.activities,
        selectedFilter: null,
      ));
    } else {
      final filtered = state.activities
          .where((activity) => activity.type == type)
          .toList();
      
      emit(state.copyWith(
        filteredActivities: filtered,
        selectedFilter: type,
      ));
    }
  }

  // Filter activities by date range
  void filterByDateRange(DateTime? startDate, DateTime? endDate) {
    List<ActivityModel> filtered = state.activities;

    if (startDate != null || endDate != null) {
      filtered = state.activities.where((activity) {
        if (startDate != null && activity.timestamp.isBefore(startDate)) {
          return false;
        }
        if (endDate != null && activity.timestamp.isAfter(endDate)) {
          return false;
        }
        return true;
      }).toList();
    }

    emit(state.copyWith(
      filteredActivities: filtered,
      startDateFilter: startDate,
      endDateFilter: endDate,
    ));
  }

  // Get activities for a specific resume
  Future<void> loadActivitiesForResume(String resumeId) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      final activities = await _activityUseCases.getActivitiesByResumeId(resumeId);
      
      emit(state.copyWith(
        activities: activities,
        filteredActivities: activities,
        isLoading: false,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      ));
    }
  }

  // Get today's activities
  Future<void> loadTodaysActivities() async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      final activities = await _activityUseCases.getTodaysActivities();
      
      emit(state.copyWith(
        activities: activities,
        filteredActivities: activities,
        isLoading: false,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      ));
    }
  }

  // Get this week's activities
  Future<void> loadThisWeeksActivities() async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      final activities = await _activityUseCases.getThisWeeksActivities();
      
      emit(state.copyWith(
        activities: activities,
        filteredActivities: activities,
        isLoading: false,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      ));
    }
  }

  // Clear all filters
  void clearFilters() {
    emit(state.copyWith(
      filteredActivities: state.activities,
      selectedFilter: null,
      startDateFilter: null,
      endDateFilter: null,
    ));
  }

  // Delete an activity
  Future<void> deleteActivity(String activityId) async {
    try {
      await _activityUseCases.deleteActivity(activityId);
      await loadRecentActivities();
    } catch (e) {
      emit(state.copyWith(errorMessage: e.toString()));
    }
  }

  // Clear all activities
  Future<void> clearAllActivities() async {
    try {
      await _activityUseCases.clearAllActivities();
      emit(state.copyWith(
        activities: [],
        filteredActivities: [],
        activityCount: 0,
      ));
    } catch (e) {
      emit(state.copyWith(errorMessage: e.toString()));
    }
  }

  // Helper methods for common activity tracking
  Future<void> trackResumeCreated(String resumeId, String resumeName) async {
    await trackActivity(
      type: ActivityType.resumeCreated,
      resumeId: resumeId,
      resumeName: resumeName,
    );
  }

  Future<void> trackResumeUpdated(String resumeId, String resumeName, {String? sectionName}) async {
    await trackActivity(
      type: ActivityType.resumeUpdated,
      resumeId: resumeId,
      resumeName: resumeName,
      sectionName: sectionName,
    );
  }

  Future<void> trackResumeSaved(String resumeId, String resumeName) async {
    await trackActivity(
      type: ActivityType.resumeSaved,
      resumeId: resumeId,
      resumeName: resumeName,
    );
  }

  Future<void> trackResumeExported(String resumeId, String resumeName) async {
    await trackActivity(
      type: ActivityType.resumeExported,
      resumeId: resumeId,
      resumeName: resumeName,
    );
  }

  Future<void> trackSectionEdited(String resumeId, String resumeName, String sectionName) async {
    await trackActivity(
      type: ActivityType.sectionEdited,
      resumeId: resumeId,
      resumeName: resumeName,
      sectionName: sectionName,
    );
  }

  Future<void> trackResumeViewed(String resumeId, String resumeName) async {
    await trackActivity(
      type: ActivityType.resumeViewed,
      resumeId: resumeId,
      resumeName: resumeName,
    );
  }
}
