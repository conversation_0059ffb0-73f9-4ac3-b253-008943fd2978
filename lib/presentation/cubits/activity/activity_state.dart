import 'package:equatable/equatable.dart';
import '../../../data/models/activity_model.dart';

class ActivityState extends Equatable {
  final List<ActivityModel> activities;
  final List<ActivityModel> filteredActivities;
  final bool isLoading;
  final String? errorMessage;
  final ActivityType? selectedFilter;
  final DateTime? startDateFilter;
  final DateTime? endDateFilter;
  final int activityCount;

  const ActivityState({
    this.activities = const [],
    this.filteredActivities = const [],
    this.isLoading = false,
    this.errorMessage,
    this.selectedFilter,
    this.startDateFilter,
    this.endDateFilter,
    this.activityCount = 0,
  });

  ActivityState copyWith({
    List<ActivityModel>? activities,
    List<ActivityModel>? filteredActivities,
    bool? isLoading,
    String? errorMessage,
    ActivityType? selectedFilter,
    DateTime? startDateFilter,
    DateTime? endDateFilter,
    int? activityCount,
  }) {
    return ActivityState(
      activities: activities ?? this.activities,
      filteredActivities: filteredActivities ?? this.filteredActivities,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      selectedFilter: selectedFilter,
      startDateFilter: startDateFilter,
      endDateFilter: endDateFilter,
      activityCount: activityCount ?? this.activityCount,
    );
  }

  @override
  List<Object?> get props => [
        activities,
        filteredActivities,
        isLoading,
        errorMessage,
        selectedFilter,
        startDateFilter,
        endDateFilter,
        activityCount,
      ];
}
