import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/constants/app_constants.dart';

part 'theme_state.dart';

class ThemeCubit extends Cubit<ThemeState> {
  final SharedPreferences _prefs;

  ThemeCubit(this._prefs) : super(const ThemeState());

  void loadTheme() {
    final themeIndex = _prefs.getInt(AppConstants.themeKey) ?? 0;
    final themeMode = ThemeMode.values[themeIndex];
    emit(state.copyWith(themeMode: themeMode));
  }

  void toggleTheme() {
    final newThemeMode = state.themeMode == ThemeMode.light
        ? ThemeMode.dark
        : ThemeMode.light;
    
    _saveTheme(newThemeMode);
    emit(state.copyWith(themeMode: newThemeMode));
  }

  void setTheme(ThemeMode themeMode) {
    _saveTheme(themeMode);
    emit(state.copyWith(themeMode: themeMode));
  }

  void _saveTheme(ThemeMode themeMode) {
    _prefs.setInt(AppConstants.themeKey, themeMode.index);
  }
}
