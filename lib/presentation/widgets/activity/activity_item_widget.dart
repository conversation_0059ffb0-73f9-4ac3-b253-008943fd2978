import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../data/models/activity_model.dart';
import '../common/shimmer_widgets.dart';

class ActivityItemWidget extends StatelessWidget {
  final ActivityModel activity;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;

  const ActivityItemWidget({
    super.key,
    required this.activity,
    this.onTap,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: _buildActivityIcon(),
        title: Text(
          activity.title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              activity.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 4),
            Text(
              _formatTimestamp(activity.timestamp),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
        trailing: onDelete != null
            ? IconButton(
                icon: const Icon(Icons.delete_outline),
                onPressed: onDelete,
                tooltip: 'Delete activity',
              )
            : null,
        onTap: onTap,
      ),
    );
  }

  Widget _buildActivityIcon() {
    IconData iconData;
    Color iconColor;

    switch (activity.type) {
      case ActivityType.resumeCreated:
        iconData = Icons.add_circle_outline;
        iconColor = Colors.green;
        break;
      case ActivityType.resumeUpdated:
        iconData = Icons.edit_outlined;
        iconColor = Colors.blue;
        break;
      case ActivityType.resumeSaved:
        iconData = Icons.save_outlined;
        iconColor = Colors.orange;
        break;
      case ActivityType.resumeExported:
        iconData = Icons.download_outlined;
        iconColor = Colors.purple;
        break;
      case ActivityType.sectionEdited:
        iconData = Icons.edit_note_outlined;
        iconColor = Colors.indigo;
        break;
      case ActivityType.templateUsed:
        iconData = Icons.palette_outlined;
        iconColor = Colors.teal;
        break;
      case ActivityType.resumeViewed:
        iconData = Icons.visibility_outlined;
        iconColor = Colors.grey;
        break;
      case ActivityType.resumeDeleted:
        iconData = Icons.delete_outline;
        iconColor = Colors.red;
        break;
    }

    return CircleAvatar(
      backgroundColor: iconColor.withOpacity(0.1),
      child: Icon(
        iconData,
        color: iconColor,
        size: 20,
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return DateFormat('MMM d, y').format(timestamp);
    }
  }
}

class ActivityListWidget extends StatelessWidget {
  final List<ActivityModel> activities;
  final bool isLoading;
  final String? errorMessage;
  final VoidCallback? onRefresh;
  final Function(ActivityModel)? onActivityTap;
  final Function(String)? onActivityDelete;

  const ActivityListWidget({
    super.key,
    required this.activities,
    this.isLoading = false,
    this.errorMessage,
    this.onRefresh,
    this.onActivityTap,
    this.onActivityDelete,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const ActivityListShimmer();
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading activities',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            if (onRefresh != null)
              ElevatedButton(
                onPressed: onRefresh,
                child: const Text('Retry'),
              ),
          ],
        ),
      );
    }

    if (activities.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 48,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'No recent activity',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Start building your resume to see activity here',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        onRefresh?.call();
      },
      child: ListView.builder(
        itemCount: activities.length,
        itemBuilder: (context, index) {
          final activity = activities[index];
          return ActivityItemWidget(
            activity: activity,
            onTap: onActivityTap != null
                ? () => onActivityTap!(activity)
                : null,
            onDelete: onActivityDelete != null
                ? () => onActivityDelete!(activity.id)
                : null,
          );
        },
      ),
    );
  }
}
