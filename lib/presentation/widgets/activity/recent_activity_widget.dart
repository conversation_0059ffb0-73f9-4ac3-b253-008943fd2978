import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../cubits/activity/activity_cubit.dart';
import '../../cubits/activity/activity_state.dart';
import '../../../data/models/activity_model.dart';
import '../common/shimmer_widgets.dart';
import 'activity_item_widget.dart';

class RecentActivityWidget extends StatefulWidget {
  final int maxItems;
  final bool showHeader;
  final VoidCallback? onViewAll;

  const RecentActivityWidget({
    super.key,
    this.maxItems = 5,
    this.showHeader = true,
    this.onViewAll,
  });

  @override
  State<RecentActivityWidget> createState() => _RecentActivityWidgetState();
}

class _RecentActivityWidgetState extends State<RecentActivityWidget> {
  @override
  void initState() {
    super.initState();
    // Load recent activities when widget is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ActivityCubit>().loadRecentActivities(limit: widget.maxItems);
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ActivityCubit, ActivityState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.showHeader) _buildHeader(context, state),
            Expanded(
              child: _buildActivityList(context, state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, ActivityState state) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Recent Activity',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          if (state.activities.isNotEmpty && widget.onViewAll != null)
            TextButton(
              onPressed: widget.onViewAll,
              child: const Text('View All'),
            ),
        ],
      ),
    );
  }

  Widget _buildActivityList(BuildContext context, ActivityState state) {
    if (state.isLoading) {
      return ListView.separated(
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: widget.maxItems,
        separatorBuilder: (context, index) => const SizedBox(height: 12),
        itemBuilder: (context, index) => const ActivityItemShimmer(),
      );
    }

    if (state.errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading activities',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              state.errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                context.read<ActivityCubit>().loadRecentActivities(limit: widget.maxItems);
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (state.activities.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No recent activity',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Start building your resume to see activity here',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    final activitiesToShow = state.activities.take(widget.maxItems).toList();

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: activitiesToShow.length,
      itemBuilder: (context, index) {
        final activity = activitiesToShow[index];
        return ActivityItemWidget(
          activity: activity,
          onTap: () => _handleActivityTap(context, activity),
        );
      },
    );
  }

  void _handleActivityTap(BuildContext context, ActivityModel activity) {
    // Handle activity tap - navigate to relevant screen based on activity type
    if (activity.resumeId != null) {
      switch (activity.type) {
        case ActivityType.resumeCreated:
        case ActivityType.resumeUpdated:
        case ActivityType.resumeSaved:
        case ActivityType.sectionEdited:
        case ActivityType.resumeViewed:
          // Navigate to resume builder
          Navigator.of(context).pushNamed('/resume-builder');
          break;
        case ActivityType.resumeExported:
          // Could show export options or recent exports
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Resume "${activity.resumeName}" was exported'),
            ),
          );
          break;
        case ActivityType.templateUsed:
          // Could navigate to templates page
          break;
        case ActivityType.resumeDeleted:
          // Show info that resume was deleted
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Resume "${activity.resumeName}" was deleted'),
            ),
          );
          break;
      }
    }
  }
}

class ActivityFilterWidget extends StatelessWidget {
  final ActivityType? selectedType;
  final Function(ActivityType?) onTypeChanged;
  final VoidCallback? onClearFilters;

  const ActivityFilterWidget({
    super.key,
    this.selectedType,
    required this.onTypeChanged,
    this.onClearFilters,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filter Activities',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              FilterChip(
                label: const Text('All'),
                selected: selectedType == null,
                onSelected: (selected) {
                  if (selected) onTypeChanged(null);
                },
              ),
              ...ActivityType.values.map((type) {
                return FilterChip(
                  label: Text(_getActivityTypeLabel(type)),
                  selected: selectedType == type,
                  onSelected: (selected) {
                    onTypeChanged(selected ? type : null);
                  },
                );
              }),
            ],
          ),
          if (selectedType != null || onClearFilters != null) ...[
            const SizedBox(height: 16),
            TextButton(
              onPressed: onClearFilters,
              child: const Text('Clear Filters'),
            ),
          ],
        ],
      ),
    );
  }

  String _getActivityTypeLabel(ActivityType type) {
    switch (type) {
      case ActivityType.resumeCreated:
        return 'Created';
      case ActivityType.resumeUpdated:
        return 'Updated';
      case ActivityType.resumeSaved:
        return 'Saved';
      case ActivityType.resumeExported:
        return 'Exported';
      case ActivityType.sectionEdited:
        return 'Section Edited';
      case ActivityType.templateUsed:
        return 'Template Used';
      case ActivityType.resumeViewed:
        return 'Viewed';
      case ActivityType.resumeDeleted:
        return 'Deleted';
    }
  }
}
