import 'package:flutter/material.dart';
import '../../../data/models/simple_resume_model.dart';
import 'resume_preview_dialog.dart';

class CompactResumePreview extends StatelessWidget {
  final ResumeModel resume;
  final VoidCallback? onTap;

  const CompactResumePreview({
    super.key,
    required this.resume,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap ?? () => showResumePreview(context, resume),
      child: Container(
        height: 200,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Text(
                    resume.personalInfo.fullName.isNotEmpty
                        ? resume.personalInfo.fullName
                        : 'Your Name',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _getContactInfo(),
                    style: const TextStyle(
                      fontSize: 10,
                      color: Colors.black54,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  
                  // Divider
                  Container(
                    height: 1,
                    color: Colors.black,
                  ),
                  const SizedBox(height: 8),
                  
                  // Summary preview
                  if (resume.summary.isNotEmpty) ...[
                    const Text(
                      'SUMMARY',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                        letterSpacing: 0.5,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      resume.summary,
                      style: const TextStyle(
                        fontSize: 8,
                        color: Colors.black,
                        height: 1.2,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                  ],
                  
                  // Work experience preview
                  if (resume.workExperience.isNotEmpty) ...[
                    const Text(
                      'WORK EXPERIENCE',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                        letterSpacing: 0.5,
                      ),
                    ),
                    const SizedBox(height: 4),
                    ...resume.workExperience.take(2).map((exp) => Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            exp.jobTitle,
                            style: const TextStyle(
                              fontSize: 9,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            exp.company,
                            style: const TextStyle(
                              fontSize: 8,
                              color: Colors.black54,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    )),
                  ],
                  
                  // Skills preview
                  if (resume.skills.isNotEmpty) ...[
                    const Spacer(),
                    const Text(
                      'SKILLS',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                        letterSpacing: 0.5,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      resume.skills.isNotEmpty
                          ? resume.skills.first.skills.take(3).join(', ')
                          : '',
                      style: const TextStyle(
                        fontSize: 8,
                        color: Colors.black,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
            
            // Overlay with tap hint
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.blue.withOpacity(0.3)),
                ),
                child: const Text(
                  'Preview',
                  style: TextStyle(
                    fontSize: 8,
                    color: Colors.blue,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getContactInfo() {
    final parts = <String>[];
    
    if (resume.personalInfo.email.isNotEmpty) {
      parts.add(resume.personalInfo.email);
    }
    if (resume.personalInfo.phone.isNotEmpty) {
      parts.add(resume.personalInfo.phone);
    }
    
    if (parts.isEmpty) {
      return '<EMAIL> | (*************';
    }
    
    return parts.join(' | ');
  }
}

class ResumePreviewCard extends StatelessWidget {
  final ResumeModel resume;
  final VoidCallback? onPreview;
  final VoidCallback? onEdit;

  const ResumePreviewCard({
    super.key,
    required this.resume,
    this.onPreview,
    this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.description, color: Colors.blue),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Resume Preview',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                TextButton.icon(
                  onPressed: onPreview ?? () => showResumePreview(context, resume),
                  icon: const Icon(Icons.fullscreen, size: 16),
                  label: const Text('Full Preview'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: CompactResumePreview(
                resume: resume,
                onTap: onPreview ?? () => showResumePreview(context, resume),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: onEdit,
                    icon: const Icon(Icons.edit, size: 16),
                    label: const Text('Edit Resume'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: onPreview ?? () => showResumePreview(context, resume),
                    icon: const Icon(Icons.preview, size: 16),
                    label: const Text('Preview'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
