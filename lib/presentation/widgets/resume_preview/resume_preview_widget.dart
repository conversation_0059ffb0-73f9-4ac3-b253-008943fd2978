import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../data/models/simple_resume_model.dart';
import '../../../data/models/resume_template_model.dart';
import '../templates/template_preview_widget.dart';

class ResumePreviewWidget extends StatelessWidget {
  final ResumeModel resume;
  final ResumeTemplateModel? template;
  final bool isFullScreen;

  const ResumePreviewWidget({
    super.key,
    required this.resume,
    this.template,
    this.isFullScreen = false,
  });

  @override
  Widget build(BuildContext context) {
    // If template is provided, use TemplatePreviewWidget for consistent styling
    if (template != null) {
      return TemplatePreviewWidget(
        resume: resume,
        template: template!,
        isCompact: false,
      );
    }

    // Fallback to default styling if no template is provided
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(32),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            if (resume.summary.isNotEmpty) ...[
              _buildSummarySection(),
              const SizedBox(height: 24),
            ],
            if (resume.workExperience.isNotEmpty) ...[
              _buildWorkExperienceSection(),
              const SizedBox(height: 24),
            ],
            if (resume.education.isNotEmpty) ...[
              _buildEducationSection(),
              const SizedBox(height: 24),
            ],
            if (resume.skills.isNotEmpty) ...[
              _buildSkillsSection(),
              const SizedBox(height: 24),
            ],
            if (resume.projects.isNotEmpty) ...[
              _buildProjectsSection(),
              const SizedBox(height: 24),
            ],
            if (resume.certifications.isNotEmpty) ...[
              _buildCertificationsSection(),
              const SizedBox(height: 24),
            ],
            if (resume.languages.isNotEmpty) ...[
              _buildLanguagesSection(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          resume.personalInfo.fullName,
          style: const TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: Colors.black,
            height: 1.2,
          ),
        ),
        const SizedBox(height: 8),
        _buildContactInfo(),
      ],
    );
  }

  Widget _buildContactInfo() {
    final contactItems = <String>[];
    
    if (resume.personalInfo.email.isNotEmpty) {
      contactItems.add(resume.personalInfo.email);
    }
    if (resume.personalInfo.phone.isNotEmpty) {
      contactItems.add(resume.personalInfo.phone);
    }
    
    final address = _buildAddress();
    if (address.isNotEmpty) {
      contactItems.add(address);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: contactItems.map((item) => Padding(
        padding: const EdgeInsets.only(bottom: 4),
        child: Text(
          item,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black87,
          ),
        ),
      )).toList(),
    );
  }

  String _buildAddress() {
    final addressParts = <String>[];
    
    if (resume.personalInfo.address.isNotEmpty) {
      addressParts.add(resume.personalInfo.address);
    }
    if (resume.personalInfo.city.isNotEmpty) {
      addressParts.add(resume.personalInfo.city);
    }
    if (resume.personalInfo.state.isNotEmpty) {
      addressParts.add(resume.personalInfo.state);
    }
    if (resume.personalInfo.zipCode.isNotEmpty) {
      addressParts.add(resume.personalInfo.zipCode);
    }
    if (resume.personalInfo.country.isNotEmpty) {
      addressParts.add(resume.personalInfo.country);
    }
    
    return addressParts.join(', ');
  }

  Widget _buildSummarySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('SUMMARY'),
        const SizedBox(height: 8),
        Text(
          resume.summary,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildWorkExperienceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('WORK EXPERIENCE'),
        const SizedBox(height: 12),
        ...resume.workExperience.map((exp) => _buildWorkExperienceItem(exp)),
      ],
    );
  }

  Widget _buildWorkExperienceItem(WorkExperienceModel experience) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            experience.jobTitle,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Text(
                experience.company,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              if (experience.location.isNotEmpty) ...[
                const Text(' • ', style: TextStyle(color: Colors.black54)),
                Text(
                  experience.location,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 2),
          Text(
            _formatDateRange(experience.startDate.toIso8601String(),
                           experience.endDate?.toIso8601String() ?? '',
                           experience.isCurrentJob),
            style: const TextStyle(
              fontSize: 13,
              color: Colors.black54,
              fontStyle: FontStyle.italic,
            ),
          ),
          if (experience.description.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              experience.description,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
                height: 1.4,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEducationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('EDUCATION'),
        const SizedBox(height: 12),
        ...resume.education.map((edu) => _buildEducationItem(edu)),
      ],
    );
  }

  Widget _buildEducationItem(EducationModel education) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            education.degree,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            education.institution,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            _formatDateRange(education.startDate.toIso8601String(),
                           education.endDate?.toIso8601String() ?? '',
                           false),
            style: const TextStyle(
              fontSize: 13,
              color: Colors.black54,
              fontStyle: FontStyle.italic,
            ),
          ),
          if (education.gpa?.isNotEmpty == true) ...[
            const SizedBox(height: 4),
            Text(
              'GPA: ${education.gpa}',
              style: const TextStyle(
                fontSize: 13,
                color: Colors.black54,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSkillsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('SKILLS'),
        const SizedBox(height: 12),
        ...resume.skills.map((skillCategory) => _buildSkillCategory(skillCategory)),
      ],
    );
  }

  Widget _buildSkillCategory(SkillCategoryModel skillCategory) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${skillCategory.category}:',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            skillCategory.skills.join(', '),
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProjectsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('PROJECTS'),
        const SizedBox(height: 12),
        ...resume.projects.map((project) => _buildProjectItem(project)),
      ],
    );
  }

  Widget _buildProjectItem(ProjectModel project) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            project.name,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          if (project.technologies.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              'Technologies: ${project.technologies.join(', ')}',
              style: const TextStyle(
                fontSize: 13,
                color: Colors.black54,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
          if (project.description.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              project.description,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
                height: 1.4,
              ),
            ),
          ],
          if (project.projectUrl?.isNotEmpty == true) ...[
            const SizedBox(height: 4),
            Text(
              'URL: ${project.projectUrl}',
              style: const TextStyle(
                fontSize: 13,
                color: Colors.blue,
                decoration: TextDecoration.underline,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCertificationsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('CERTIFICATIONS'),
        const SizedBox(height: 12),
        ...resume.certifications.map((cert) => _buildCertificationItem(cert)),
      ],
    );
  }

  Widget _buildCertificationItem(CertificationModel certification) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            certification.name,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            certification.issuer,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            'Issued: ${DateFormat('MMM yyyy').format(certification.issueDate)}',
            style: const TextStyle(
              fontSize: 13,
              color: Colors.black54,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguagesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('LANGUAGES'),
        const SizedBox(height: 12),
        Wrap(
          spacing: 16,
          runSpacing: 8,
          children: resume.languages.map((lang) => _buildLanguageItem(lang)).toList(),
        ),
      ],
    );
  }

  Widget _buildLanguageItem(LanguageModel language) {
    return Text(
      '${language.language} (${language.proficiency})',
      style: const TextStyle(
        fontSize: 14,
        color: Colors.black,
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Container(
      padding: const EdgeInsets.only(bottom: 4),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.black,
            width: 1,
          ),
        ),
      ),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.black,
          letterSpacing: 1.2,
        ),
      ),
    );
  }

  String _formatDateRange(String startDate, String endDate, bool isCurrent) {
    if (startDate.isEmpty) return '';
    
    String formattedStart = startDate;
    String formattedEnd = isCurrent ? 'Present' : endDate;
    
    try {
      if (startDate.isNotEmpty) {
        final start = DateTime.parse(startDate);
        formattedStart = DateFormat('MMM yyyy').format(start);
      }
      if (endDate.isNotEmpty && !isCurrent) {
        final end = DateTime.parse(endDate);
        formattedEnd = DateFormat('MMM yyyy').format(end);
      }
    } catch (e) {
      // If parsing fails, use the original strings
    }
    
    return '$formattedStart - $formattedEnd';
  }
}
