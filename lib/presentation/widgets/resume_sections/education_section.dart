import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:uuid/uuid.dart';
import 'package:intl/intl.dart';

import '../../cubits/resume/resume_cubit.dart';
import '../../../data/models/simple_resume_model.dart';

class EducationSection extends StatefulWidget {
  const EducationSection({super.key});

  @override
  State<EducationSection> createState() => _EducationSectionState();
}

class _EducationSectionState extends State<EducationSection> {
  final Uuid _uuid = const Uuid();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ResumeCubit, ResumeState>(
      builder: (context, state) {
        final education = state.currentResume?.education ?? [];

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Education',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  ElevatedButton.icon(
                    onPressed: () => _showAddEducationDialog(context),
                    icon: const Icon(Icons.add),
                    label: const Text('Add Education'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: education.isEmpty
                    ? _buildEmptyState(context)
                    : _buildEducationList(context, education),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.school_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: 16),
          Text(
            'No Education Added',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            'Add your educational background to showcase your qualifications',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showAddEducationDialog(context),
            icon: const Icon(Icons.add),
            label: const Text('Add Your Education'),
          ),
        ],
      ),
    );
  }

  Widget _buildEducationList(BuildContext context, List<EducationModel> educationList) {
    return ListView.builder(
      itemCount: educationList.length,
      itemBuilder: (context, index) {
        final education = educationList[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: ListTile(
            title: Text(education.degree),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('${education.institution} • ${education.location}'),
                Text(_formatDateRange(education.startDate, education.endDate, education.isCurrentlyStudying)),
                if (education.gpa != null) Text('GPA: ${education.gpa}'),
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'edit') {
                  _showEditEducationDialog(context, education, index);
                } else if (value == 'delete') {
                  context.read<ResumeCubit>().removeEducation(index);
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(value: 'edit', child: Text('Edit')),
                const PopupMenuItem(value: 'delete', child: Text('Delete')),
              ],
            ),
          ),
        );
      },
    );
  }

  String _formatDateRange(DateTime startDate, DateTime? endDate, bool isCurrentlyStudying) {
    final startFormatted = DateFormat('MMM yyyy').format(startDate);
    if (isCurrentlyStudying) {
      return '$startFormatted - Present';
    } else if (endDate != null) {
      final endFormatted = DateFormat('MMM yyyy').format(endDate);
      return '$startFormatted - $endFormatted';
    }
    return startFormatted;
  }

  void _showAddEducationDialog(BuildContext context) {
    _showEducationDialog(context, null, null);
  }

  void _showEditEducationDialog(BuildContext context, EducationModel education, int index) {
    _showEducationDialog(context, education, index);
  }

  void _showEducationDialog(BuildContext context, EducationModel? education, int? index) {
    final formKey = GlobalKey<FormBuilderState>();
    final isEditing = education != null;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.7,
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Text(
                isEditing ? 'Edit Education' : 'Add Education',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const Divider(),
              Expanded(
                child: FormBuilder(
                  key: formKey,
                  initialValue: {
                    'degree': education?.degree ?? '',
                    'institution': education?.institution ?? '',
                    'location': education?.location ?? '',
                    'startDate': education?.startDate,
                    'endDate': education?.endDate,
                    'isCurrentlyStudying': education?.isCurrentlyStudying ?? false,
                    'gpa': education?.gpa ?? '',
                    'description': education?.description ?? '',
                  },
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        FormBuilderTextField(
                          name: 'degree',
                          decoration: const InputDecoration(labelText: 'Degree *'),
                          validator: FormBuilderValidators.required(),
                        ),
                        const SizedBox(height: 16),
                        FormBuilderTextField(
                          name: 'institution',
                          decoration: const InputDecoration(labelText: 'Institution *'),
                          validator: FormBuilderValidators.required(),
                        ),
                        const SizedBox(height: 16),
                        FormBuilderTextField(
                          name: 'location',
                          decoration: const InputDecoration(labelText: 'Location *'),
                          validator: FormBuilderValidators.required(),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: FormBuilderDateTimePicker(
                                name: 'startDate',
                                inputType: InputType.date,
                                decoration: const InputDecoration(labelText: 'Start Date *'),
                                validator: FormBuilderValidators.required(),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: FormBuilderDateTimePicker(
                                name: 'endDate',
                                inputType: InputType.date,
                                decoration: const InputDecoration(labelText: 'End Date'),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        FormBuilderCheckbox(
                          name: 'isCurrentlyStudying',
                          title: const Text('Currently studying here'),
                        ),
                        const SizedBox(height: 16),
                        FormBuilderTextField(
                          name: 'gpa',
                          decoration: const InputDecoration(labelText: 'GPA (optional)'),
                        ),
                        const SizedBox(height: 16),
                        FormBuilderTextField(
                          name: 'description',
                          decoration: const InputDecoration(labelText: 'Description (optional)'),
                          maxLines: 3,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () => _saveEducation(context, formKey, isEditing, index),
                    child: Text(isEditing ? 'Update' : 'Add'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _saveEducation(BuildContext context, GlobalKey<FormBuilderState> formKey, bool isEditing, int? index) {
    if (formKey.currentState?.saveAndValidate() ?? false) {
      final formData = formKey.currentState!.value;

      final education = EducationModel(
        id: isEditing ? context.read<ResumeCubit>().state.currentResume!.education[index!].id : _uuid.v4(),
        degree: formData['degree'] as String,
        institution: formData['institution'] as String,
        location: formData['location'] as String,
        startDate: formData['startDate'] as DateTime,
        endDate: formData['isCurrentlyStudying'] == true ? null : formData['endDate'] as DateTime?,
        isCurrentlyStudying: formData['isCurrentlyStudying'] as bool,
        gpa: formData['gpa'] as String?,
        description: formData['description'] as String?,
      );

      if (isEditing && index != null) {
        context.read<ResumeCubit>().updateEducation(index, education);
      } else {
        context.read<ResumeCubit>().addEducation(education);
      }

      Navigator.of(context).pop();
    }
  }
}
