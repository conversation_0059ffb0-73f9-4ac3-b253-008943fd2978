import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:uuid/uuid.dart';
import 'package:intl/intl.dart';

import '../../cubits/resume/resume_cubit.dart';
import '../../../data/models/simple_resume_model.dart';

class WorkExperienceSection extends StatefulWidget {
  const WorkExperienceSection({super.key});

  @override
  State<WorkExperienceSection> createState() => _WorkExperienceSectionState();
}

class _WorkExperienceSectionState extends State<WorkExperienceSection> {
  final Uuid _uuid = const Uuid();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ResumeCubit, ResumeState>(
      builder: (context, state) {
        final workExperience = state.currentResume?.workExperience ?? [];

        // Debug logging
        print('WorkExperienceSection: Resume loaded: ${state.currentResume != null}');
        print('WorkExperienceSection: Work experience count: ${workExperience.length}');
        if (workExperience.isNotEmpty) {
          print('WorkExperienceSection: First item: ${workExperience.first.jobTitle}');
        }

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Work Experience',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  ElevatedButton.icon(
                    onPressed: () => _showAddExperienceDialog(context),
                    icon: const Icon(Icons.add),
                    label: const Text('Add Experience'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: workExperience.isEmpty
                    ? _buildEmptyState(context)
                    : _buildExperienceList(context, workExperience),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.work_outline,
            size: 64,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: 16),
          Text(
            'No Work Experience Added',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            'Add your professional experience to showcase your career journey',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showAddExperienceDialog(context),
            icon: const Icon(Icons.add),
            label: const Text('Add Your First Experience'),
          ),
        ],
      ),
    );
  }

  Widget _buildExperienceList(BuildContext context, List<WorkExperienceModel> experiences) {
    return ListView.builder(
      itemCount: experiences.length,
      itemBuilder: (context, index) {
        final experience = experiences[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            experience.jobTitle,
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${experience.company} • ${experience.location}',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _formatDateRange(experience.startDate, experience.endDate, experience.isCurrentJob),
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        switch (value) {
                          case 'edit':
                            _showEditExperienceDialog(context, experience, index);
                            break;
                          case 'delete':
                            _showDeleteConfirmation(context, index);
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit),
                              SizedBox(width: 8),
                              Text('Edit'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, color: Colors.red),
                              SizedBox(width: 8),
                              Text('Delete', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                if (experience.description.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Text(
                    experience.description,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
                if (experience.achievements.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Text(
                    'Key Achievements:',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...experience.achievements.map((achievement) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          Icons.check_circle,
                          size: 16,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            achievement,
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ),
                      ],
                    ),
                  )),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  String _formatDateRange(DateTime startDate, DateTime? endDate, bool isCurrentJob) {
    final startFormatted = DateFormat('MMM yyyy').format(startDate);
    if (isCurrentJob) {
      return '$startFormatted - Present';
    } else if (endDate != null) {
      final endFormatted = DateFormat('MMM yyyy').format(endDate);
      return '$startFormatted - $endFormatted';
    } else {
      return startFormatted;
    }
  }

  void _showAddExperienceDialog(BuildContext context) {
    _showExperienceDialog(context, null, null);
  }

  void _showEditExperienceDialog(BuildContext context, WorkExperienceModel experience, int index) {
    _showExperienceDialog(context, experience, index);
  }

  void _showDeleteConfirmation(BuildContext context, int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Experience'),
        content: const Text('Are you sure you want to delete this work experience?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<ResumeCubit>().removeWorkExperience(index);
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showExperienceDialog(BuildContext context, WorkExperienceModel? experience, int? index) {
    final formKey = GlobalKey<FormBuilderState>();
    final isEditing = experience != null;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    isEditing ? 'Edit Work Experience' : 'Add Work Experience',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const Divider(),
              Expanded(
                child: FormBuilder(
                  key: formKey,
                  initialValue: {
                    'jobTitle': experience?.jobTitle ?? '',
                    'company': experience?.company ?? '',
                    'location': experience?.location ?? '',
                    'startDate': experience?.startDate,
                    'endDate': experience?.endDate,
                    'isCurrentJob': experience?.isCurrentJob ?? false,
                    'description': experience?.description ?? '',
                    'achievements': experience?.achievements.join('\n') ?? '',
                  },
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        FormBuilderTextField(
                          name: 'jobTitle',
                          decoration: const InputDecoration(
                            labelText: 'Job Title *',
                            hintText: 'e.g., Senior Software Engineer',
                          ),
                          validator: FormBuilderValidators.required(),
                        ),
                        const SizedBox(height: 16),
                        FormBuilderTextField(
                          name: 'company',
                          decoration: const InputDecoration(
                            labelText: 'Company *',
                            hintText: 'e.g., Google Inc.',
                          ),
                          validator: FormBuilderValidators.required(),
                        ),
                        const SizedBox(height: 16),
                        FormBuilderTextField(
                          name: 'location',
                          decoration: const InputDecoration(
                            labelText: 'Location *',
                            hintText: 'e.g., San Francisco, CA',
                          ),
                          validator: FormBuilderValidators.required(),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: FormBuilderDateTimePicker(
                                name: 'startDate',
                                inputType: InputType.date,
                                decoration: const InputDecoration(
                                  labelText: 'Start Date *',
                                ),
                                validator: FormBuilderValidators.required(),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: FormBuilderDateTimePicker(
                                name: 'endDate',
                                inputType: InputType.date,
                                decoration: const InputDecoration(
                                  labelText: 'End Date',
                                ),
                                enabled: !(formKey.currentState?.fields['isCurrentJob']?.value ?? false),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        FormBuilderCheckbox(
                          name: 'isCurrentJob',
                          title: const Text('I currently work here'),
                          onChanged: (value) {
                            if (value == true) {
                              formKey.currentState?.fields['endDate']?.didChange(null);
                            }
                          },
                        ),
                        const SizedBox(height: 16),
                        FormBuilderTextField(
                          name: 'description',
                          decoration: const InputDecoration(
                            labelText: 'Job Description',
                            hintText: 'Describe your role and responsibilities...',
                            alignLabelWithHint: true,
                          ),
                          maxLines: 4,
                        ),
                        const SizedBox(height: 16),
                        FormBuilderTextField(
                          name: 'achievements',
                          decoration: const InputDecoration(
                            labelText: 'Key Achievements',
                            hintText: 'List your achievements (one per line)...',
                            alignLabelWithHint: true,
                          ),
                          maxLines: 4,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () => _saveExperience(context, formKey, isEditing, index),
                    child: Text(isEditing ? 'Update' : 'Add'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _saveExperience(BuildContext context, GlobalKey<FormBuilderState> formKey, bool isEditing, int? index) {
    if (formKey.currentState?.saveAndValidate() ?? false) {
      final formData = formKey.currentState!.value;

      final experience = WorkExperienceModel(
        id: isEditing ? context.read<ResumeCubit>().state.currentResume!.workExperience[index!].id : _uuid.v4(),
        jobTitle: formData['jobTitle'] as String,
        company: formData['company'] as String,
        location: formData['location'] as String,
        startDate: formData['startDate'] as DateTime,
        endDate: formData['isCurrentJob'] == true ? null : formData['endDate'] as DateTime?,
        isCurrentJob: formData['isCurrentJob'] as bool,
        description: formData['description'] as String? ?? '',
        achievements: (formData['achievements'] as String? ?? '')
            .split('\n')
            .where((line) => line.trim().isNotEmpty)
            .toList(),
      );

      if (isEditing && index != null) {
        context.read<ResumeCubit>().updateWorkExperience(index, experience);
      } else {
        context.read<ResumeCubit>().addWorkExperience(experience);
      }

      Navigator.of(context).pop();
    }
  }
}
