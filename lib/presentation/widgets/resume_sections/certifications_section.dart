import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:uuid/uuid.dart';
import 'package:intl/intl.dart';

import '../../cubits/resume/resume_cubit.dart';
import '../../../data/models/simple_resume_model.dart';

class CertificationsSection extends StatefulWidget {
  const CertificationsSection({super.key});

  @override
  State<CertificationsSection> createState() => _CertificationsSectionState();
}

class _CertificationsSectionState extends State<CertificationsSection> {
  final Uuid _uuid = const Uuid();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ResumeCubit, ResumeState>(
      builder: (context, state) {
        final certifications = state.currentResume?.certifications ?? [];

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Certifications',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  ElevatedButton.icon(
                    onPressed: () => _showAddCertificationDialog(context),
                    icon: const Icon(Icons.add),
                    label: const Text('Add Certification'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: certifications.isEmpty
                    ? _buildEmptyState(context)
                    : _buildCertificationsList(context, certifications),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.verified_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: 16),
          Text(
            'No Certifications Added',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            'Add your professional certifications and credentials',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showAddCertificationDialog(context),
            icon: const Icon(Icons.add),
            label: const Text('Add Certification'),
          ),
        ],
      ),
    );
  }

  Widget _buildCertificationsList(BuildContext context, List<CertificationModel> certifications) {
    return ListView.builder(
      itemCount: certifications.length,
      itemBuilder: (context, index) {
        final certification = certifications[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: ListTile(
            title: Text(certification.name),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(certification.issuer),
                Text('Issued: ${DateFormat('MMM yyyy').format(certification.issueDate)}'),
                if (certification.expiryDate != null)
                  Text('Expires: ${DateFormat('MMM yyyy').format(certification.expiryDate!)}'),
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'edit') {
                  _showEditCertificationDialog(context, certification, index);
                } else if (value == 'delete') {
                  context.read<ResumeCubit>().removeCertification(index);
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(value: 'edit', child: Text('Edit')),
                const PopupMenuItem(value: 'delete', child: Text('Delete')),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showAddCertificationDialog(BuildContext context) {
    _showCertificationDialog(context, null, null);
  }

  void _showEditCertificationDialog(BuildContext context, CertificationModel certification, int index) {
    _showCertificationDialog(context, certification, index);
  }

  void _showCertificationDialog(BuildContext context, CertificationModel? certification, int? index) {
    final formKey = GlobalKey<FormBuilderState>();
    final isEditing = certification != null;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.7,
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Text(
                isEditing ? 'Edit Certification' : 'Add Certification',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const Divider(),
              Expanded(
                child: FormBuilder(
                  key: formKey,
                  initialValue: {
                    'name': certification?.name ?? '',
                    'issuer': certification?.issuer ?? '',
                    'issueDate': certification?.issueDate,
                    'expiryDate': certification?.expiryDate,
                    'credentialId': certification?.credentialId ?? '',
                    'credentialUrl': certification?.credentialUrl ?? '',
                  },
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        FormBuilderTextField(
                          name: 'name',
                          decoration: const InputDecoration(labelText: 'Certification Name *'),
                          validator: FormBuilderValidators.required(),
                        ),
                        const SizedBox(height: 16),
                        FormBuilderTextField(
                          name: 'issuer',
                          decoration: const InputDecoration(labelText: 'Issuing Organization *'),
                          validator: FormBuilderValidators.required(),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: FormBuilderDateTimePicker(
                                name: 'issueDate',
                                inputType: InputType.date,
                                decoration: const InputDecoration(labelText: 'Issue Date *'),
                                validator: FormBuilderValidators.required(),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: FormBuilderDateTimePicker(
                                name: 'expiryDate',
                                inputType: InputType.date,
                                decoration: const InputDecoration(labelText: 'Expiry Date'),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        FormBuilderTextField(
                          name: 'credentialId',
                          decoration: const InputDecoration(labelText: 'Credential ID'),
                        ),
                        const SizedBox(height: 16),
                        FormBuilderTextField(
                          name: 'credentialUrl',
                          decoration: const InputDecoration(labelText: 'Credential URL'),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () => _saveCertification(context, formKey, isEditing, index),
                    child: Text(isEditing ? 'Update' : 'Add'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _saveCertification(BuildContext context, GlobalKey<FormBuilderState> formKey, bool isEditing, int? index) {
    if (formKey.currentState?.saveAndValidate() ?? false) {
      final formData = formKey.currentState!.value;

      final certification = CertificationModel(
        id: isEditing ? context.read<ResumeCubit>().state.currentResume!.certifications[index!].id : _uuid.v4(),
        name: formData['name'] as String,
        issuer: formData['issuer'] as String,
        issueDate: formData['issueDate'] as DateTime,
        expiryDate: formData['expiryDate'] as DateTime?,
        credentialId: formData['credentialId'] as String?,
        credentialUrl: formData['credentialUrl'] as String?,
      );

      if (isEditing && index != null) {
        context.read<ResumeCubit>().updateCertification(index, certification);
      } else {
        context.read<ResumeCubit>().addCertification(certification);
      }

      Navigator.of(context).pop();
    }
  }
}
