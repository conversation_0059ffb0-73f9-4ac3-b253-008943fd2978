import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';

import '../../cubits/resume/resume_cubit.dart';
import '../../../core/constants/app_constants.dart';

class SummarySection extends StatefulWidget {
  const SummarySection({super.key});

  @override
  State<SummarySection> createState() => _SummarySectionState();
}

class _SummarySectionState extends State<SummarySection> {
  final _formKey = GlobalKey<FormBuilderState>();
  final _summaryController = TextEditingController();
  String _summary = '';

  @override
  void initState() {
    super.initState();
    final resume = context.read<ResumeCubit>().state.currentResume;
    _summary = resume?.summary ?? '';
    _summaryController.text = _summary;
  }

  @override
  void dispose() {
    _summaryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ResumeCubit, ResumeState>(
      listener: (context, state) {
        if (state.currentResume != null && state.currentResume!.summary != _summary) {
          _summary = state.currentResume!.summary;
          _summaryController.text = _summary;
        }
      },
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Professional Summary',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                'Write a compelling summary that highlights your key qualifications and career objectives.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 24),

              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: FormBuilder(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        FormBuilderTextField(
                          name: 'summary',
                          controller: _summaryController,
                          decoration: const InputDecoration(
                            labelText: 'Professional Summary',
                            hintText: 'Experienced professional with expertise in...',
                            border: OutlineInputBorder(),
                            alignLabelWithHint: true,
                          ),
                          maxLines: 8,
                          maxLength: AppConstants.maxSummaryLength,
                          validator: FormBuilderValidators.compose([
                            FormBuilderValidators.required(),
                            FormBuilderValidators.minLength(50),
                            FormBuilderValidators.maxLength(AppConstants.maxSummaryLength),
                          ]),
                          onChanged: (value) {
                            if (value != null) {
                              _updateSummary(value);
                            }
                          },
                        ),
                        const SizedBox(height: 16),
                        
                        // Character count
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '${_summaryController.text.length}/${AppConstants.maxSummaryLength} characters',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            if (_summaryController.text.length > AppConstants.maxSummaryLength * 0.8)
                              Icon(
                                Icons.warning,
                                size: 16,
                                color: _summaryController.text.length > AppConstants.maxSummaryLength
                                    ? Theme.of(context).colorScheme.error
                                    : Theme.of(context).colorScheme.secondary,
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Tips Card
              Card(
                color: Theme.of(context).colorScheme.primaryContainer,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.lightbulb,
                            color: Theme.of(context).colorScheme.onPrimaryContainer,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Tips for a Great Summary',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onPrimaryContainer,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      _buildTip('Keep it concise (2-4 sentences)'),
                      _buildTip('Highlight your most relevant skills and experience'),
                      _buildTip('Include specific achievements with numbers when possible'),
                      _buildTip('Tailor it to the job you\'re applying for'),
                      _buildTip('Use action words and industry keywords'),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Sample Summaries
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Sample Summaries',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 12),
                      _buildSampleSummary(
                        'Software Developer',
                        'Experienced software developer with 5+ years of expertise in full-stack development using React, Node.js, and Python. Successfully delivered 20+ web applications, improving user engagement by 40%. Passionate about clean code and agile methodologies.',
                      ),
                      const SizedBox(height: 12),
                      _buildSampleSummary(
                        'Marketing Manager',
                        'Results-driven marketing manager with 7 years of experience in digital marketing and brand management. Led campaigns that increased brand awareness by 60% and generated \$2M in revenue. Expert in SEO, social media marketing, and data analytics.',
                      ),
                      const SizedBox(height: 12),
                      _buildSampleSummary(
                        'Project Manager',
                        'Certified PMP with 8+ years managing cross-functional teams and delivering complex projects on time and under budget. Successfully managed projects worth \$5M+, improving efficiency by 35%. Strong background in Agile and Waterfall methodologies.',
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTip(String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.check_circle,
            size: 16,
            color: Theme.of(context).colorScheme.onPrimaryContainer,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              tip,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSampleSummary(String title, String summary) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () => _useSample(summary),
                child: const Text('Use This'),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            summary,
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
    );
  }

  void _updateSummary(String summary) {
    context.read<ResumeCubit>().updateSummary(summary);
  }

  void _useSample(String sample) {
    _summaryController.text = sample;
    _updateSummary(sample);
  }
}
