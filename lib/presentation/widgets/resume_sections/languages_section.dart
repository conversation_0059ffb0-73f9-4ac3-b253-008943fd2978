import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:uuid/uuid.dart';

import '../../cubits/resume/resume_cubit.dart';
import '../../../data/models/simple_resume_model.dart';
import '../../../core/constants/app_constants.dart';

class LanguagesSection extends StatefulWidget {
  const LanguagesSection({super.key});

  @override
  State<LanguagesSection> createState() => _LanguagesSectionState();
}

class _LanguagesSectionState extends State<LanguagesSection> {
  final Uuid _uuid = const Uuid();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ResumeCubit, ResumeState>(
      builder: (context, state) {
        final languages = state.currentResume?.languages ?? [];

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Languages',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  ElevatedButton.icon(
                    onPressed: () => _showAddLanguageDialog(context),
                    icon: const Icon(Icons.add),
                    label: const Text('Add Language'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: languages.isEmpty
                    ? _buildEmptyState(context)
                    : _buildLanguagesList(context, languages),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.language_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: 16),
          Text(
            'No Languages Added',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            'Add languages you speak and your proficiency level',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showAddLanguageDialog(context),
            icon: const Icon(Icons.add),
            label: const Text('Add Language'),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguagesList(BuildContext context, List<LanguageModel> languages) {
    return ListView.builder(
      itemCount: languages.length,
      itemBuilder: (context, index) {
        final language = languages[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            title: Text(language.language),
            subtitle: Text(language.proficiency),
            trailing: PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'edit') {
                  _showEditLanguageDialog(context, language, index);
                } else if (value == 'delete') {
                  context.read<ResumeCubit>().removeLanguage(index);
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(value: 'edit', child: Text('Edit')),
                const PopupMenuItem(value: 'delete', child: Text('Delete')),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showAddLanguageDialog(BuildContext context) {
    _showLanguageDialog(context, null, null);
  }

  void _showEditLanguageDialog(BuildContext context, LanguageModel language, int index) {
    _showLanguageDialog(context, language, index);
  }

  void _showLanguageDialog(BuildContext context, LanguageModel? language, int? index) {
    final formKey = GlobalKey<FormBuilderState>();
    final isEditing = language != null;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isEditing ? 'Edit Language' : 'Add Language'),
        content: FormBuilder(
          key: formKey,
          initialValue: {
            'language': language?.language ?? '',
            'proficiency': language?.proficiency ?? AppConstants.languageLevels.first,
          },
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              FormBuilderTextField(
                name: 'language',
                decoration: const InputDecoration(labelText: 'Language *'),
                validator: FormBuilderValidators.required(),
              ),
              const SizedBox(height: 16),
              FormBuilderDropdown<String>(
                name: 'proficiency',
                decoration: const InputDecoration(labelText: 'Proficiency Level *'),
                validator: FormBuilderValidators.required(),
                items: AppConstants.languageLevels.map((level) =>
                  DropdownMenuItem(value: level, child: Text(level))
                ).toList(),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _saveLanguage(context, formKey, isEditing, index),
            child: Text(isEditing ? 'Update' : 'Add'),
          ),
        ],
      ),
    );
  }

  void _saveLanguage(BuildContext context, GlobalKey<FormBuilderState> formKey, bool isEditing, int? index) {
    if (formKey.currentState?.saveAndValidate() ?? false) {
      final formData = formKey.currentState!.value;

      final language = LanguageModel(
        id: isEditing ? context.read<ResumeCubit>().state.currentResume!.languages[index!].id : _uuid.v4(),
        language: formData['language'] as String,
        proficiency: formData['proficiency'] as String,
      );

      if (isEditing && index != null) {
        context.read<ResumeCubit>().updateLanguage(index, language);
      } else {
        context.read<ResumeCubit>().addLanguage(language);
      }

      Navigator.of(context).pop();
    }
  }
}
