import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:uuid/uuid.dart';

import '../../cubits/resume/resume_cubit.dart';
import '../../../data/models/simple_resume_model.dart';

class ProjectsSection extends StatefulWidget {
  const ProjectsSection({super.key});

  @override
  State<ProjectsSection> createState() => _ProjectsSectionState();
}

class _ProjectsSectionState extends State<ProjectsSection> {
  final Uuid _uuid = const Uuid();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ResumeCubit, ResumeState>(
      builder: (context, state) {
        final projects = state.currentResume?.projects ?? [];

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Projects',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  ElevatedButton.icon(
                    onPressed: () => _showAddProjectDialog(context),
                    icon: const Icon(Icons.add),
                    label: const Text('Add Project'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: projects.isEmpty
                    ? _buildEmptyState(context)
                    : _buildProjectsList(context, projects),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.code_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: 16),
          Text(
            'No Projects Added',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            'Showcase your projects and achievements',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showAddProjectDialog(context),
            icon: const Icon(Icons.add),
            label: const Text('Add Your First Project'),
          ),
        ],
      ),
    );
  }

  Widget _buildProjectsList(BuildContext context, List<ProjectModel> projects) {
    return ListView.builder(
      itemCount: projects.length,
      itemBuilder: (context, index) {
        final project = projects[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        project.name,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        if (value == 'edit') {
                          _showEditProjectDialog(context, project, index);
                        } else if (value == 'delete') {
                          context.read<ResumeCubit>().removeProject(index);
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(value: 'edit', child: Text('Edit')),
                        const PopupMenuItem(value: 'delete', child: Text('Delete')),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  project.description,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                if (project.technologies.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 4,
                    children: project.technologies.map((tech) => Chip(
                      label: Text(tech),
                      backgroundColor: Theme.of(context).colorScheme.secondaryContainer,
                    )).toList(),
                  ),
                ],
                if (project.projectUrl != null || project.githubUrl != null) ...[
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      if (project.projectUrl != null)
                        TextButton.icon(
                          onPressed: () {
                            // TODO: Launch URL
                          },
                          icon: const Icon(Icons.link),
                          label: const Text('View Project'),
                        ),
                      if (project.githubUrl != null)
                        TextButton.icon(
                          onPressed: () {
                            // TODO: Launch URL
                          },
                          icon: const Icon(Icons.code),
                          label: const Text('GitHub'),
                        ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  void _showAddProjectDialog(BuildContext context) {
    _showProjectDialog(context, null, null);
  }

  void _showEditProjectDialog(BuildContext context, ProjectModel project, int index) {
    _showProjectDialog(context, project, index);
  }

  void _showProjectDialog(BuildContext context, ProjectModel? project, int? index) {
    final formKey = GlobalKey<FormBuilderState>();
    final isEditing = project != null;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Text(
                isEditing ? 'Edit Project' : 'Add Project',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const Divider(),
              Expanded(
                child: FormBuilder(
                  key: formKey,
                  initialValue: {
                    'name': project?.name ?? '',
                    'description': project?.description ?? '',
                    'technologies': project?.technologies.join(', ') ?? '',
                    'projectUrl': project?.projectUrl ?? '',
                    'githubUrl': project?.githubUrl ?? '',
                    'achievements': project?.achievements.join('\n') ?? '',
                  },
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        FormBuilderTextField(
                          name: 'name',
                          decoration: const InputDecoration(labelText: 'Project Name *'),
                          validator: FormBuilderValidators.required(),
                        ),
                        const SizedBox(height: 16),
                        FormBuilderTextField(
                          name: 'description',
                          decoration: const InputDecoration(
                            labelText: 'Description *',
                            alignLabelWithHint: true,
                          ),
                          maxLines: 3,
                          validator: FormBuilderValidators.required(),
                        ),
                        const SizedBox(height: 16),
                        FormBuilderTextField(
                          name: 'technologies',
                          decoration: const InputDecoration(
                            labelText: 'Technologies (comma separated)',
                            hintText: 'e.g., Flutter, Firebase, Node.js',
                          ),
                        ),
                        const SizedBox(height: 16),
                        FormBuilderTextField(
                          name: 'projectUrl',
                          decoration: const InputDecoration(labelText: 'Project URL'),
                        ),
                        const SizedBox(height: 16),
                        FormBuilderTextField(
                          name: 'githubUrl',
                          decoration: const InputDecoration(labelText: 'GitHub URL'),
                        ),
                        const SizedBox(height: 16),
                        FormBuilderTextField(
                          name: 'achievements',
                          decoration: const InputDecoration(
                            labelText: 'Key Achievements',
                            hintText: 'List achievements (one per line)...',
                            alignLabelWithHint: true,
                          ),
                          maxLines: 3,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () => _saveProject(context, formKey, isEditing, index),
                    child: Text(isEditing ? 'Update' : 'Add'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _saveProject(BuildContext context, GlobalKey<FormBuilderState> formKey, bool isEditing, int? index) {
    if (formKey.currentState?.saveAndValidate() ?? false) {
      final formData = formKey.currentState!.value;

      final project = ProjectModel(
        id: isEditing ? context.read<ResumeCubit>().state.currentResume!.projects[index!].id : _uuid.v4(),
        name: formData['name'] as String,
        description: formData['description'] as String,
        technologies: (formData['technologies'] as String? ?? '')
            .split(',')
            .map((s) => s.trim())
            .where((s) => s.isNotEmpty)
            .toList(),
        projectUrl: formData['projectUrl'] as String?,
        githubUrl: formData['githubUrl'] as String?,
        achievements: (formData['achievements'] as String? ?? '')
            .split('\n')
            .where((line) => line.trim().isNotEmpty)
            .toList(),
      );

      if (isEditing && index != null) {
        context.read<ResumeCubit>().updateProject(index, project);
      } else {
        context.read<ResumeCubit>().addProject(project);
      }

      Navigator.of(context).pop();
    }
  }
}
