import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:uuid/uuid.dart';

import '../../cubits/resume/resume_cubit.dart';
import '../../../data/models/simple_resume_model.dart';
import '../../../core/constants/app_constants.dart';

class SkillsSection extends StatefulWidget {
  const SkillsSection({super.key});

  @override
  State<SkillsSection> createState() => _SkillsSectionState();
}

class _SkillsSectionState extends State<SkillsSection> {
  final Uuid _uuid = const Uuid();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ResumeCubit, ResumeState>(
      builder: (context, state) {
        final skillCategories = state.currentResume?.skills ?? [];

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Skills',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  ElevatedButton.icon(
                    onPressed: () => _showAddSkillCategoryDialog(context),
                    icon: const Icon(Icons.add),
                    label: const Text('Add Category'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: skillCategories.isEmpty
                    ? _buildEmptyState(context)
                    : _buildSkillsList(context, skillCategories),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.star_outline,
            size: 64,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: 16),
          Text(
            'No Skills Added',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            'Add your skills organized by categories',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showAddSkillCategoryDialog(context),
            icon: const Icon(Icons.add),
            label: const Text('Add Skill Category'),
          ),
        ],
      ),
    );
  }

  Widget _buildSkillsList(BuildContext context, List<SkillCategoryModel> skillCategories) {
    return ListView.builder(
      itemCount: skillCategories.length,
      itemBuilder: (context, index) {
        final category = skillCategories[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      category.category,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        if (value == 'edit') {
                          _showEditSkillCategoryDialog(context, category, index);
                        } else if (value == 'delete') {
                          context.read<ResumeCubit>().removeSkillCategory(index);
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(value: 'edit', child: Text('Edit')),
                        const PopupMenuItem(value: 'delete', child: Text('Delete')),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: category.skills.map((skill) => Chip(
                    label: Text(skill.name),
                    backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                  )).toList(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showAddSkillCategoryDialog(BuildContext context) {
    _showSkillCategoryDialog(context, null, null);
  }

  void _showEditSkillCategoryDialog(BuildContext context, SkillCategoryModel category, int index) {
    _showSkillCategoryDialog(context, category, index);
  }

  void _showSkillCategoryDialog(BuildContext context, SkillCategoryModel? category, int? index) {
    final formKey = GlobalKey<FormBuilderState>();
    final isEditing = category != null;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.6,
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Text(
                isEditing ? 'Edit Skill Category' : 'Add Skill Category',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const Divider(),
              Expanded(
                child: FormBuilder(
                  key: formKey,
                  initialValue: {
                    'category': category?.category ?? '',
                    'skills': category?.skills.map((s) => s.name).join(', ') ?? '',
                  },
                  child: Column(
                    children: [
                      FormBuilderDropdown<String>(
                        name: 'category',
                        decoration: const InputDecoration(labelText: 'Category *'),
                        validator: FormBuilderValidators.required(),
                        items: AppConstants.skillCategories.map((cat) =>
                          DropdownMenuItem(value: cat, child: Text(cat))
                        ).toList(),
                      ),
                      const SizedBox(height: 16),
                      FormBuilderTextField(
                        name: 'skills',
                        decoration: const InputDecoration(
                          labelText: 'Skills (comma separated) *',
                          hintText: 'e.g., Flutter, Dart, Firebase, Git',
                        ),
                        validator: FormBuilderValidators.required(),
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () => _saveSkillCategory(context, formKey, isEditing, index),
                    child: Text(isEditing ? 'Update' : 'Add'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _saveSkillCategory(BuildContext context, GlobalKey<FormBuilderState> formKey, bool isEditing, int? index) {
    if (formKey.currentState?.saveAndValidate() ?? false) {
      final formData = formKey.currentState!.value;

      final skillsText = formData['skills'] as String;
      final skillNames = skillsText.split(',').map((s) => s.trim()).where((s) => s.isNotEmpty).toList();

      final skills = skillNames.map((name) => SkillModel(
        id: _uuid.v4(),
        name: name,
        proficiencyLevel: 3, // Default proficiency
      )).toList();

      final skillCategory = SkillCategoryModel(
        id: isEditing ? context.read<ResumeCubit>().state.currentResume!.skills[index!].id : _uuid.v4(),
        category: formData['category'] as String,
        skills: skills,
      );

      if (isEditing && index != null) {
        context.read<ResumeCubit>().updateSkillCategory(index, skillCategory);
      } else {
        context.read<ResumeCubit>().addSkillCategory(skillCategory);
      }

      Navigator.of(context).pop();
    }
  }
}
