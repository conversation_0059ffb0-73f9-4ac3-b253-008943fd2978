import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../data/models/resume_template_model.dart';
import '../../../data/models/simple_resume_model.dart';
import '../../cubits/template/template_cubit.dart';
import 'template_preview_widget.dart';

class TemplatePreviewDialog extends StatelessWidget {
  final ResumeTemplateModel template;
  final ResumeModel? sampleResume;

  const TemplatePreviewDialog({
    super.key,
    required this.template,
    this.sampleResume,
  });

  @override
  Widget build(BuildContext context) {
    final resume = sampleResume ?? TemplateRepository.getSampleResumeData();
    
    return Dialog.fullscreen(
      child: Scaffold(
        appBar: AppBar(
          title: Text('${template.name} Preview'),
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              context.read<TemplateCubit>().closePreview();
              Navigator.of(context).pop();
            },
          ),
          actions: [
            if (template.isPremium)
              Container(
                margin: const EdgeInsets.only(right: 8),
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.amber,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Text(
                  'PREMIUM',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ),
            TextButton.icon(
              onPressed: () => _selectTemplate(context),
              icon: const Icon(Icons.check),
              label: const Text('Select Template'),
            ),
            const SizedBox(width: 8),
          ],
        ),
        body: Container(
          color: Colors.grey.shade100,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Center(
              child: Container(
                constraints: const BoxConstraints(maxWidth: 800),
                child: Column(
                  children: [
                    // Template info card
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    template.style.primaryColor,
                                    template.style.secondaryColor,
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                template.previewIcon,
                                color: Colors.white,
                                size: 30,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        template.name,
                                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      if (template.isPremium)
                                        Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                          decoration: BoxDecoration(
                                            color: Colors.amber,
                                            borderRadius: BorderRadius.circular(12),
                                          ),
                                          child: const Text(
                                            'PREMIUM',
                                            style: TextStyle(
                                              fontSize: 10,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.black,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    template.description,
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Wrap(
                                    spacing: 8,
                                    runSpacing: 4,
                                    children: [
                                      _buildStyleChip('Category', _getCategoryDisplayName(template.category)),
                                      _buildStyleChip('Layout', _getLayoutDisplayName(template.style.layout)),
                                      _buildStyleChip('Font', template.style.fontFamily),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                    // Template preview
                    Container(
                      width: double.infinity,
                      constraints: const BoxConstraints(minHeight: 600),
                      child: TemplatePreviewWidget(
                        resume: resume,
                        template: template,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        bottomNavigationBar: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    context.read<TemplateCubit>().closePreview();
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _selectTemplate(context),
                  child: const Text('Select Template'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStyleChip(String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        '$label: $value',
        style: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  void _selectTemplate(BuildContext context) {
    context.read<TemplateCubit>().selectTemplate(template);
    Navigator.of(context).pop();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Selected "${template.name}" template'),
        action: SnackBarAction(
          label: 'Create Resume',
          onPressed: () {
            // Navigate to resume builder or creation page
            // This would depend on your app's navigation structure
          },
        ),
      ),
    );
  }

  String _getCategoryDisplayName(TemplateCategory category) {
    switch (category) {
      case TemplateCategory.professional:
        return 'Professional';
      case TemplateCategory.creative:
        return 'Creative';
      case TemplateCategory.modern:
        return 'Modern';
      case TemplateCategory.minimal:
        return 'Minimal';
    }
  }

  String _getLayoutDisplayName(TemplateLayout layout) {
    switch (layout) {
      case TemplateLayout.singleColumn:
        return 'Single Column';
      case TemplateLayout.twoColumn:
        return 'Two Column';
      case TemplateLayout.sidebar:
        return 'Sidebar';
      case TemplateLayout.modern:
        return 'Modern';
    }
  }
}

// Compact template preview for smaller spaces
class CompactTemplatePreview extends StatelessWidget {
  final ResumeTemplateModel template;
  final ResumeModel? sampleResume;
  final VoidCallback? onTap;

  const CompactTemplatePreview({
    super.key,
    required this.template,
    this.sampleResume,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final resume = sampleResume ?? TemplateRepository.getSampleResumeData();
    
    return GestureDetector(
      onTap: onTap ?? () => _showFullPreview(context),
      child: Container(
        height: 300,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: TemplatePreviewWidget(
            resume: resume,
            template: template,
            isCompact: true,
          ),
        ),
      ),
    );
  }

  void _showFullPreview(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => TemplatePreviewDialog(
        template: template,
        sampleResume: sampleResume,
      ),
    );
  }
}

// Helper function to show template preview
void showTemplatePreview(BuildContext context, ResumeTemplateModel template, {ResumeModel? sampleResume}) {
  showDialog(
    context: context,
    builder: (context) => TemplatePreviewDialog(
      template: template,
      sampleResume: sampleResume,
    ),
  );
}
