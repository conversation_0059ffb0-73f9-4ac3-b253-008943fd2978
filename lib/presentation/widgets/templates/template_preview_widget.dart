import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../data/models/simple_resume_model.dart';
import '../../../data/models/resume_template_model.dart';

class TemplatePreviewWidget extends StatelessWidget {
  final ResumeModel resume;
  final ResumeTemplateModel template;
  final bool isCompact;

  const TemplatePreviewWidget({
    super.key,
    required this.resume,
    required this.template,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        padding: EdgeInsets.all(isCompact ? 16 : 32),
        child: _buildLayout(),
      ),
    );
  }

  Widget _buildLayout() {
    switch (template.style.layout) {
      case TemplateLayout.twoColumn:
        return _buildTwoColumnLayout();
      case TemplateLayout.sidebar:
        return _buildSidebarLayout();
      case TemplateLayout.modern:
        return _buildModernLayout();
      case TemplateLayout.singleColumn:
        return _buildSingleColumnLayout();
    }
  }

  Widget _buildSingleColumnLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(),
        _buildSpacing(),
        if (resume.summary.isNotEmpty) ...[
          _buildSummarySection(),
          _buildSpacing(),
        ],
        if (resume.workExperience.isNotEmpty) ...[
          _buildWorkExperienceSection(),
          _buildSpacing(),
        ],
        if (resume.education.isNotEmpty) ...[
          _buildEducationSection(),
          _buildSpacing(),
        ],
        if (resume.skills.isNotEmpty) ...[
          _buildSkillsSection(),
          _buildSpacing(),
        ],
        if (resume.projects.isNotEmpty) ...[
          _buildProjectsSection(),
          _buildSpacing(),
        ],
        if (resume.certifications.isNotEmpty) ...[
          _buildCertificationsSection(),
        ],
      ],
    );
  }

  Widget _buildTwoColumnLayout() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left column (main content)
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              _buildSpacing(),
              if (resume.summary.isNotEmpty) ...[
                _buildSummarySection(),
                _buildSpacing(),
              ],
              if (resume.workExperience.isNotEmpty) ...[
                _buildWorkExperienceSection(),
                _buildSpacing(),
              ],
              if (resume.projects.isNotEmpty) ...[
                _buildProjectsSection(),
              ],
            ],
          ),
        ),
        const SizedBox(width: 24),
        // Right column (sidebar)
        Expanded(
          flex: 1,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (resume.skills.isNotEmpty) ...[
                _buildSkillsSection(),
                _buildSpacing(),
              ],
              if (resume.education.isNotEmpty) ...[
                _buildEducationSection(),
                _buildSpacing(),
              ],
              if (resume.certifications.isNotEmpty) ...[
                _buildCertificationsSection(),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSidebarLayout() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Sidebar (left)
        Container(
          width: 200,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: template.style.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildContactInfo(),
              _buildSpacing(),
              if (resume.skills.isNotEmpty) ...[
                _buildSkillsSection(),
                _buildSpacing(),
              ],
              if (resume.education.isNotEmpty) ...[
                _buildEducationSection(),
              ],
            ],
          ),
        ),
        const SizedBox(width: 24),
        // Main content (right)
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeaderName(),
              _buildSpacing(),
              if (resume.summary.isNotEmpty) ...[
                _buildSummarySection(),
                _buildSpacing(),
              ],
              if (resume.workExperience.isNotEmpty) ...[
                _buildWorkExperienceSection(),
                _buildSpacing(),
              ],
              if (resume.projects.isNotEmpty) ...[
                _buildProjectsSection(),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildModernLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Modern header with gradient background
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                template.style.primaryColor,
                template.style.secondaryColor,
              ],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                resume.personalInfo.fullName,
                style: TextStyle(
                  fontSize: template.style.headerFontSize,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontFamily: template.style.fontFamily,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                resume.personalInfo.email,
                style: TextStyle(
                  fontSize: template.style.bodyFontSize,
                  color: Colors.white.withValues(alpha: 0.9),
                  fontFamily: template.style.fontFamily,
                ),
              ),
            ],
          ),
        ),
        _buildSpacing(),
        if (resume.summary.isNotEmpty) ...[
          _buildSummarySection(),
          _buildSpacing(),
        ],
        // Rest of content in two columns
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (resume.workExperience.isNotEmpty) ...[
                    _buildWorkExperienceSection(),
                    _buildSpacing(),
                  ],
                  if (resume.projects.isNotEmpty) ...[
                    _buildProjectsSection(),
                  ],
                ],
              ),
            ),
            const SizedBox(width: 24),
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (resume.skills.isNotEmpty) ...[
                    _buildSkillsSection(),
                    _buildSpacing(),
                  ],
                  if (resume.education.isNotEmpty) ...[
                    _buildEducationSection(),
                  ],
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSpacing() {
    return SizedBox(height: template.style.sectionSpacing);
  }

  Widget _buildHeader() {
    if (template.style.layout == TemplateLayout.modern) {
      return const SizedBox.shrink(); // Header is built in modern layout
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          resume.personalInfo.fullName,
          style: TextStyle(
            fontSize: template.style.headerFontSize,
            fontWeight: FontWeight.bold,
            color: template.style.primaryColor,
            fontFamily: template.style.fontFamily,
          ),
        ),
        const SizedBox(height: 8),
        _buildContactInfo(),
      ],
    );
  }

  Widget _buildHeaderName() {
    return Text(
      resume.personalInfo.fullName,
      style: TextStyle(
        fontSize: template.style.headerFontSize,
        fontWeight: FontWeight.bold,
        color: template.style.primaryColor,
        fontFamily: template.style.fontFamily,
      ),
    );
  }

  Widget _buildContactInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          resume.personalInfo.email,
          style: TextStyle(
            fontSize: template.style.bodyFontSize,
            color: template.style.secondaryColor,
            fontFamily: template.style.fontFamily,
          ),
        ),
        Text(
          resume.personalInfo.phone,
          style: TextStyle(
            fontSize: template.style.bodyFontSize,
            color: template.style.secondaryColor,
            fontFamily: template.style.fontFamily,
          ),
        ),
        Text(
          '${resume.personalInfo.city}, ${resume.personalInfo.state}',
          style: TextStyle(
            fontSize: template.style.bodyFontSize,
            color: template.style.secondaryColor,
            fontFamily: template.style.fontFamily,
          ),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Row(
      children: [
        if (template.style.showSectionIcons) ...[
          Icon(
            _getSectionIcon(title),
            size: 20,
            color: template.style.accentColor,
          ),
          const SizedBox(width: 8),
        ],
        Text(
          title,
          style: TextStyle(
            fontSize: template.style.bodyFontSize + 2,
            fontWeight: FontWeight.bold,
            color: template.style.primaryColor,
            fontFamily: template.style.fontFamily,
          ),
        ),
      ],
    );
  }

  IconData _getSectionIcon(String title) {
    switch (title.toLowerCase()) {
      case 'summary':
        return Icons.person;
      case 'work experience':
        return Icons.work;
      case 'education':
        return Icons.school;
      case 'skills':
        return Icons.star;
      case 'projects':
        return Icons.code;
      case 'certifications':
        return Icons.verified;
      default:
        return Icons.circle;
    }
  }

  Widget _buildSummarySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('SUMMARY'),
        const SizedBox(height: 8),
        Text(
          resume.summary,
          style: TextStyle(
            fontSize: template.style.bodyFontSize,
            color: Colors.black87,
            height: 1.5,
            fontFamily: template.style.fontFamily,
          ),
        ),
      ],
    );
  }

  Widget _buildWorkExperienceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('WORK EXPERIENCE'),
        const SizedBox(height: 12),
        ...resume.workExperience.take(isCompact ? 2 : resume.workExperience.length)
            .map((exp) => _buildWorkExperienceItem(exp)),
      ],
    );
  }

  Widget _buildWorkExperienceItem(WorkExperienceModel experience) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            experience.jobTitle,
            style: TextStyle(
              fontSize: template.style.bodyFontSize + 1,
              fontWeight: FontWeight.bold,
              color: template.style.primaryColor,
              fontFamily: template.style.fontFamily,
            ),
          ),
          Text(
            '${experience.company} • ${experience.location}',
            style: TextStyle(
              fontSize: template.style.bodyFontSize,
              color: template.style.secondaryColor,
              fontFamily: template.style.fontFamily,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            _formatDateRange(experience.startDate.toIso8601String(),
                           experience.endDate?.toIso8601String() ?? '',
                           experience.isCurrentJob),
            style: TextStyle(
              fontSize: template.style.bodyFontSize - 1,
              color: Colors.black54,
              fontStyle: FontStyle.italic,
              fontFamily: template.style.fontFamily,
            ),
          ),
          if (experience.description.isNotEmpty && !isCompact) ...[
            const SizedBox(height: 8),
            Text(
              experience.description,
              style: TextStyle(
                fontSize: template.style.bodyFontSize,
                color: Colors.black87,
                height: 1.4,
                fontFamily: template.style.fontFamily,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEducationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('EDUCATION'),
        const SizedBox(height: 12),
        ...resume.education.take(isCompact ? 1 : resume.education.length)
            .map((edu) => _buildEducationItem(edu)),
      ],
    );
  }

  Widget _buildEducationItem(EducationModel education) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            education.degree,
            style: TextStyle(
              fontSize: template.style.bodyFontSize + 1,
              fontWeight: FontWeight.bold,
              color: template.style.primaryColor,
              fontFamily: template.style.fontFamily,
            ),
          ),
          Text(
            education.institution,
            style: TextStyle(
              fontSize: template.style.bodyFontSize,
              color: template.style.secondaryColor,
              fontFamily: template.style.fontFamily,
            ),
          ),
          if (education.gpa?.isNotEmpty == true) ...[
            Text(
              'GPA: ${education.gpa}',
              style: TextStyle(
                fontSize: template.style.bodyFontSize - 1,
                color: Colors.black54,
                fontFamily: template.style.fontFamily,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSkillsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('SKILLS'),
        const SizedBox(height: 12),
        ...resume.skills.take(isCompact ? 2 : resume.skills.length)
            .map((skillCategory) => _buildSkillCategory(skillCategory)),
      ],
    );
  }

  Widget _buildSkillCategory(SkillCategoryModel skillCategory) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            skillCategory.category,
            style: TextStyle(
              fontSize: template.style.bodyFontSize,
              fontWeight: FontWeight.w600,
              color: template.style.primaryColor,
              fontFamily: template.style.fontFamily,
            ),
          ),
          const SizedBox(height: 4),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: skillCategory.skills.map((skill) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: template.style.accentColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: template.style.accentColor.withValues(alpha: 0.5)),
              ),
              child: Text(
                skill.name,
                style: TextStyle(
                  fontSize: template.style.bodyFontSize - 1,
                  color: template.style.primaryColor,
                  fontFamily: template.style.fontFamily,
                ),
              ),
            )).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildProjectsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('PROJECTS'),
        const SizedBox(height: 12),
        ...resume.projects.take(isCompact ? 1 : resume.projects.length)
            .map((project) => _buildProjectItem(project)),
      ],
    );
  }

  Widget _buildProjectItem(ProjectModel project) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            project.name,
            style: TextStyle(
              fontSize: template.style.bodyFontSize + 1,
              fontWeight: FontWeight.bold,
              color: template.style.primaryColor,
              fontFamily: template.style.fontFamily,
            ),
          ),
          if (project.technologies.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              'Technologies: ${project.technologies.join(', ')}',
              style: TextStyle(
                fontSize: template.style.bodyFontSize - 1,
                color: Colors.black54,
                fontStyle: FontStyle.italic,
                fontFamily: template.style.fontFamily,
              ),
            ),
          ],
          if (project.description.isNotEmpty && !isCompact) ...[
            const SizedBox(height: 8),
            Text(
              project.description,
              style: TextStyle(
                fontSize: template.style.bodyFontSize,
                color: Colors.black87,
                height: 1.4,
                fontFamily: template.style.fontFamily,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCertificationsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('CERTIFICATIONS'),
        const SizedBox(height: 12),
        ...resume.certifications.take(isCompact ? 1 : resume.certifications.length)
            .map((cert) => _buildCertificationItem(cert)),
      ],
    );
  }

  Widget _buildCertificationItem(CertificationModel certification) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            certification.name,
            style: TextStyle(
              fontSize: template.style.bodyFontSize + 1,
              fontWeight: FontWeight.bold,
              color: template.style.primaryColor,
              fontFamily: template.style.fontFamily,
            ),
          ),
          Text(
            certification.issuer,
            style: TextStyle(
              fontSize: template.style.bodyFontSize,
              color: template.style.secondaryColor,
              fontFamily: template.style.fontFamily,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateRange(String startDate, String endDate, bool isCurrent) {
    try {
      final start = DateTime.parse(startDate);
      final startFormatted = DateFormat('MMM yyyy').format(start);

      if (isCurrent) {
        return '$startFormatted - Present';
      } else if (endDate.isNotEmpty) {
        final end = DateTime.parse(endDate);
        final endFormatted = DateFormat('MMM yyyy').format(end);
        return '$startFormatted - $endFormatted';
      } else {
        return startFormatted;
      }
    } catch (e) {
      return 'Date not available';
    }
  }
}
