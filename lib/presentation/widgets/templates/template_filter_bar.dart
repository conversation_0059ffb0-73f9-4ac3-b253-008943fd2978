import 'package:flutter/material.dart';

import '../../../data/models/resume_template_model.dart';

class TemplateFilterBar extends StatelessWidget {
  final List<TemplateCategory> categories;
  final TemplateCategory? selectedCategory;
  final bool showOnlyFree;
  final Function(TemplateCategory?) onCategorySelected;
  final VoidCallback onTogglePremiumFilter;
  final VoidCallback onClearFilters;

  const TemplateFilterBar({
    super.key,
    required this.categories,
    required this.selectedCategory,
    required this.showOnlyFree,
    required this.onCategorySelected,
    required this.onTogglePremiumFilter,
    required this.onClearFilters,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Category Filters
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                // All Categories
                _buildCategoryChip(
                  context,
                  'All',
                  selectedCategory == null,
                  () => onCategorySelected(null),
                ),
                const SizedBox(width: 8),
                
                // Individual Categories
                ...categories.map((category) => Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: _buildCategoryChip(
                    context,
                    _getCategoryDisplayName(category),
                    selectedCategory == category,
                    () => onCategorySelected(category),
                  ),
                )),
              ],
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Additional Filters
          Row(
            children: [
              // Free Templates Filter
              FilterChip(
                label: const Text('Free Only'),
                selected: showOnlyFree,
                onSelected: (_) => onTogglePremiumFilter(),
                avatar: Icon(
                  showOnlyFree ? Icons.check : Icons.star_border,
                  size: 16,
                ),
              ),
              
              const Spacer(),
              
              // Clear Filters
              if (selectedCategory != null || showOnlyFree)
                TextButton.icon(
                  onPressed: onClearFilters,
                  icon: const Icon(Icons.clear_all, size: 16),
                  label: const Text('Clear'),
                  style: TextButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.primary,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(
    BuildContext context,
    String label,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (_) => onTap(),
      backgroundColor: Theme.of(context).colorScheme.surface,
      selectedColor: Theme.of(context).colorScheme.primaryContainer,
      checkmarkColor: Theme.of(context).colorScheme.primary,
      labelStyle: TextStyle(
        color: isSelected
            ? Theme.of(context).colorScheme.primary
            : Theme.of(context).colorScheme.onSurface,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
    );
  }

  String _getCategoryDisplayName(TemplateCategory category) {
    switch (category) {
      case TemplateCategory.professional:
        return 'Professional';
      case TemplateCategory.creative:
        return 'Creative';
      case TemplateCategory.modern:
        return 'Modern';
      case TemplateCategory.minimal:
        return 'Minimal';
    }
  }
}
