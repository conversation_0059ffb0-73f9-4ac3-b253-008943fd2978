class AppConstants {
  // App Info
  static const String appName = 'ATS Resume Builder';
  static const String appVersion = '1.0.0';
  
  // Storage Keys
  static const String themeKey = 'theme_mode';
  static const String userDataKey = 'user_data';
  static const String resumeDataKey = 'resume_data';
  static const String authTokenKey = 'auth_token';
  
  // Firebase Collections
  static const String usersCollection = 'users';
  static const String resumesCollection = 'resumes';
  static const String templatesCollection = 'templates';
  
  // Hive Boxes
  static const String settingsBox = 'settings';
  static const String resumeBox = 'resume';
  static const String userBox = 'user';
  static const String activityBox = 'activity';
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double defaultBorderRadius = 8.0;
  static const double cardElevation = 2.0;
  
  // Form Validation
  static const int minPasswordLength = 6;
  static const int maxNameLength = 50;
  static const int maxSummaryLength = 500;
  static const int maxDescriptionLength = 1000;
  
  // PDF Export
  static const String defaultFileName = 'resume';
  static const String pdfExtension = '.pdf';
  
  // Error Messages
  static const String networkError = 'Network error. Please check your connection.';
  static const String unknownError = 'An unknown error occurred.';
  static const String authError = 'Authentication failed.';
  static const String validationError = 'Please check your input.';
  
  // Success Messages
  static const String saveSuccess = 'Data saved successfully!';
  static const String exportSuccess = 'Resume exported successfully!';
  static const String authSuccess = 'Authentication successful!';
  
  // Resume Sections
  static const List<String> resumeSections = [
    'Personal Information',
    'Summary',
    'Work Experience',
    'Education',
    'Skills',
    'Projects',
    'Certifications',
    'Languages',
  ];
  
  // Skill Categories
  static const List<String> skillCategories = [
    'Technical Skills',
    'Soft Skills',
    'Additional Skills',
  ];
  
  // Language Proficiency Levels
  static const List<String> languageLevels = [
    'Beginner',
    'Intermediate',
    'Advanced',
    'Native',
  ];
  
  // Social Media Platforms
  static const List<String> socialPlatforms = [
    'LinkedIn',
    'GitHub',
    'Twitter',
    'Portfolio',
    'Behance',
    'Dribbble',
  ];
}
