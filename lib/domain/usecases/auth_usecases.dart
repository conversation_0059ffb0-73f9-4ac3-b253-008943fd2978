import '../repositories/auth_repository.dart';
import '../../data/models/user_model.dart';

class AuthUseCases {
  final AuthRepository _authRepository;

  AuthUseCases(this._authRepository);

  Future<UserModel?> getCurrentUser() async {
    return await _authRepository.getCurrentUser();
  }

  Future<UserModel> signInWithEmail(String email, String password) async {
    return await _authRepository.signInWithEmail(email, password);
  }

  Future<UserModel> signUpWithEmail(String email, String password, String displayName) async {
    return await _authRepository.signUpWithEmail(email, password, displayName);
  }

  Future<UserModel> signInWithGoogle() async {
    return await _authRepository.signInWithGoogle();
  }

  Future<UserModel> signInWithApple() async {
    return await _authRepository.signInWithApple();
  }

  Future<void> signOut() async {
    return await _authRepository.signOut();
  }

  Future<void> resetPassword(String email) async {
    return await _authRepository.resetPassword(email);
  }

  Future<void> updateProfile(String displayName, String? photoUrl) async {
    return await _authRepository.updateProfile(displayName, photoUrl);
  }

  Future<void> deleteAccount() async {
    return await _authRepository.deleteAccount();
  }
}
