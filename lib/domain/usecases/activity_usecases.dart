import '../repositories/activity_repository.dart';
import '../../data/models/activity_model.dart';
import 'package:uuid/uuid.dart';

class ActivityUseCases {
  final ActivityRepository _repository;
  final Uuid _uuid = const Uuid();

  ActivityUseCases(this._repository);

  Future<void> trackActivity({
    required ActivityType type,
    required String resumeId,
    required String resumeName,
    String? sectionName,
    Map<String, dynamic>? metadata,
  }) async {
    final activity = ActivityFactory.createResumeActivity(
      id: _uuid.v4(),
      type: type,
      resumeId: resumeId,
      resumeName: resumeName,
      sectionName: sectionName,
      metadata: metadata,
    );

    await _repository.saveActivity(activity);
  }

  Future<void> trackCustomActivity({
    required String title,
    required String description,
    String? resumeId,
    String? resumeName,
    String? sectionName,
    Map<String, dynamic>? metadata,
  }) async {
    final activity = ActivityModel(
      id: _uuid.v4(),
      type: ActivityType.resumeUpdated, // Default type for custom activities
      title: title,
      description: description,
      timestamp: DateTime.now(),
      resumeId: resumeId,
      resumeName: resumeName,
      sectionName: sectionName,
      metadata: metadata,
    );

    await _repository.saveActivity(activity);
  }

  Future<List<ActivityModel>> getRecentActivities({int limit = 20}) async {
    return await _repository.getRecentActivities(limit: limit);
  }

  Future<List<ActivityModel>> getAllActivities() async {
    return await _repository.getAllActivities();
  }

  Future<List<ActivityModel>> getActivitiesByType(ActivityType type) async {
    return await _repository.getActivitiesByType(type);
  }

  Future<List<ActivityModel>> getActivitiesByResumeId(String resumeId) async {
    return await _repository.getActivitiesByResumeId(resumeId);
  }

  Future<List<ActivityModel>> getActivitiesInDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    return await _repository.getActivitiesInDateRange(
      startDate: startDate,
      endDate: endDate,
    );
  }

  Future<List<ActivityModel>> getTodaysActivities() async {
    final now = DateTime.now();
    final startOfDay = DateTime(now.year, now.month, now.day);
    final endOfDay = DateTime(now.year, now.month, now.day, 23, 59, 59);

    return await getActivitiesInDateRange(
      startDate: startOfDay,
      endDate: endOfDay,
    );
  }

  Future<List<ActivityModel>> getThisWeeksActivities() async {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final startOfWeekDay = DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day);

    return await getActivitiesInDateRange(
      startDate: startOfWeekDay,
      endDate: now,
    );
  }

  Future<void> deleteActivity(String activityId) async {
    await _repository.deleteActivity(activityId);
  }

  Future<void> clearAllActivities() async {
    await _repository.clearAllActivities();
  }

  Future<void> clearOldActivities({int keepCount = 50}) async {
    await _repository.clearOldActivities(keepCount: keepCount);
  }

  Future<int> getActivityCount() async {
    return await _repository.getActivityCount();
  }

  Future<ActivityModel?> getLastActivity() async {
    return await _repository.getLastActivity();
  }

  // Helper methods for common activity tracking scenarios
  Future<void> trackResumeCreated(String resumeId, String resumeName) async {
    await trackActivity(
      type: ActivityType.resumeCreated,
      resumeId: resumeId,
      resumeName: resumeName,
    );
  }

  Future<void> trackResumeUpdated(String resumeId, String resumeName, {String? sectionName}) async {
    await trackActivity(
      type: ActivityType.resumeUpdated,
      resumeId: resumeId,
      resumeName: resumeName,
      sectionName: sectionName,
    );
  }

  Future<void> trackResumeSaved(String resumeId, String resumeName) async {
    await trackActivity(
      type: ActivityType.resumeSaved,
      resumeId: resumeId,
      resumeName: resumeName,
    );
  }

  Future<void> trackResumeExported(String resumeId, String resumeName) async {
    await trackActivity(
      type: ActivityType.resumeExported,
      resumeId: resumeId,
      resumeName: resumeName,
    );
  }

  Future<void> trackSectionEdited(String resumeId, String resumeName, String sectionName) async {
    await trackActivity(
      type: ActivityType.sectionEdited,
      resumeId: resumeId,
      resumeName: resumeName,
      sectionName: sectionName,
    );
  }

  Future<void> trackTemplateUsed(String resumeId, String resumeName, String templateName) async {
    await trackActivity(
      type: ActivityType.templateUsed,
      resumeId: resumeId,
      resumeName: resumeName,
      metadata: {'templateName': templateName},
    );
  }

  Future<void> trackResumeViewed(String resumeId, String resumeName) async {
    await trackActivity(
      type: ActivityType.resumeViewed,
      resumeId: resumeId,
      resumeName: resumeName,
    );
  }

  Future<void> trackResumeDeleted(String resumeId, String resumeName) async {
    await trackActivity(
      type: ActivityType.resumeDeleted,
      resumeId: resumeId,
      resumeName: resumeName,
    );
  }
}
