import '../../data/models/activity_model.dart';

abstract class ActivityRepository {
  Future<void> saveActivity(ActivityModel activity);
  Future<List<ActivityModel>> getAllActivities();
  Future<List<ActivityModel>> getRecentActivities({int limit = 20});
  Future<List<ActivityModel>> getActivitiesByType(ActivityType type);
  Future<List<ActivityModel>> getActivitiesByResumeId(String resumeId);
  Future<List<ActivityModel>> getActivitiesInDateRange({
    required DateTime startDate,
    required DateTime endDate,
  });
  Future<void> deleteActivity(String activityId);
  Future<void> clearAllActivities();
  Future<void> clearOldActivities({int keepCount = 50});
  Future<int> getActivityCount();
  Future<ActivityModel?> getLastActivity();
}
