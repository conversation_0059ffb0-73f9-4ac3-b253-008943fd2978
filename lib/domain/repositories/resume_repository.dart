import '../../data/models/simple_resume_model.dart';
import '../../data/models/resume_template_model.dart';

abstract class ResumeRepository {
  Future<ResumeModel> getResume(String resumeId);
  Future<List<ResumeModel>> getUserResumes(String userId);
  Future<void> saveResume(ResumeModel resume);
  Future<void> deleteResume(String resumeId);
  Future<ResumeModel> duplicateResume(String resumeId);
  Future<void> exportToPdf(ResumeModel resume, {ResumeTemplateModel? template});
  Future<String> shareResume(String resumeId);
  Future<void> saveResumeLocally(ResumeModel resume);
  Future<ResumeModel?> getLocalResume();
}
