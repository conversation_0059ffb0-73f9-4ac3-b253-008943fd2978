import 'package:equatable/equatable.dart';

class UserModel extends Equatable {
  final String id;
  final String email;
  final String? displayName;
  final String? photoUrl;
  final bool emailVerified;
  final DateTime createdAt;
  final DateTime lastSignIn;
  final List<String> resumeIds;

  const UserModel({
    required this.id,
    required this.email,
    this.displayName,
    this.photoUrl,
    required this.emailVerified,
    required this.createdAt,
    required this.lastSignIn,
    required this.resumeIds,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      email: json['email'] as String,
      displayName: json['displayName'] as String?,
      photoUrl: json['photoUrl'] as String?,
      emailVerified: json['emailVerified'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastSignIn: DateTime.parse(json['lastSignIn'] as String),
      resumeIds: List<String>.from(json['resumeIds'] as List? ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'displayName': displayName,
      'photoUrl': photoUrl,
      'emailVerified': emailVerified,
      'createdAt': createdAt.toIso8601String(),
      'lastSignIn': lastSignIn.toIso8601String(),
      'resumeIds': resumeIds,
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? displayName,
    String? photoUrl,
    bool? emailVerified,
    DateTime? createdAt,
    DateTime? lastSignIn,
    List<String>? resumeIds,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoUrl: photoUrl ?? this.photoUrl,
      emailVerified: emailVerified ?? this.emailVerified,
      createdAt: createdAt ?? this.createdAt,
      lastSignIn: lastSignIn ?? this.lastSignIn,
      resumeIds: resumeIds ?? this.resumeIds,
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        displayName,
        photoUrl,
        emailVerified,
        createdAt,
        lastSignIn,
        resumeIds,
      ];
}

class AuthStateModel extends Equatable {
  final bool isAuthenticated;
  final UserModel? user;
  final String? errorMessage;
  final bool isLoading;

  const AuthStateModel({
    required this.isAuthenticated,
    this.user,
    this.errorMessage,
    required this.isLoading,
  });

  factory AuthStateModel.fromJson(Map<String, dynamic> json) {
    return AuthStateModel(
      isAuthenticated: json['isAuthenticated'] as bool,
      user: json['user'] != null ? UserModel.fromJson(json['user'] as Map<String, dynamic>) : null,
      errorMessage: json['errorMessage'] as String?,
      isLoading: json['isLoading'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isAuthenticated': isAuthenticated,
      'user': user?.toJson(),
      'errorMessage': errorMessage,
      'isLoading': isLoading,
    };
  }

  AuthStateModel copyWith({
    bool? isAuthenticated,
    UserModel? user,
    String? errorMessage,
    bool? isLoading,
  }) {
    return AuthStateModel(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      user: user ?? this.user,
      errorMessage: errorMessage ?? this.errorMessage,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  @override
  List<Object?> get props => [isAuthenticated, user, errorMessage, isLoading];
}
