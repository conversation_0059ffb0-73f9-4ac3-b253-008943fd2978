import 'package:equatable/equatable.dart';

class ResumeModel extends Equatable {
  final String id;
  final PersonalInfoModel personalInfo;
  final String summary;
  final List<WorkExperienceModel> workExperience;
  final List<EducationModel> education;
  final List<ProjectModel> projects;
  final List<SkillCategoryModel> skills;
  final List<LanguageModel> languages;
  final List<CertificationModel> certifications;
  final List<SocialMediaModel> socialMedia;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ResumeModel({
    required this.id,
    required this.personalInfo,
    required this.summary,
    required this.workExperience,
    required this.education,
    required this.projects,
    required this.skills,
    required this.languages,
    required this.certifications,
    required this.socialMedia,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ResumeModel.fromJson(Map<String, dynamic> json) {
    try {
      return ResumeModel(
        id: json['id'] as String? ?? '',
        personalInfo: json['personalInfo'] != null
            ? PersonalInfoModel.fromJson(json['personalInfo'] as Map<String, dynamic>)
            : const PersonalInfoModel(
                firstName: '', lastName: '', email: '', phone: '',
                address: '', city: '', state: '', zipCode: '', country: ''
              ),
        summary: json['summary'] as String? ?? '',
        workExperience: (json['workExperience'] as List? ?? [])
            .map((e) => e is Map<String, dynamic>
                ? WorkExperienceModel.fromJson(e)
                : e as WorkExperienceModel)
            .cast<WorkExperienceModel>()
            .toList(),
        education: (json['education'] as List? ?? [])
            .map((e) => e is Map<String, dynamic>
                ? EducationModel.fromJson(e)
                : e as EducationModel)
            .cast<EducationModel>()
            .toList(),
        projects: (json['projects'] as List? ?? [])
            .map((e) => e is Map<String, dynamic>
                ? ProjectModel.fromJson(e)
                : e as ProjectModel)
            .cast<ProjectModel>()
            .toList(),
        skills: (json['skills'] as List? ?? [])
            .map((e) => e is Map<String, dynamic>
                ? SkillCategoryModel.fromJson(e)
                : e as SkillCategoryModel)
            .cast<SkillCategoryModel>()
            .toList(),
        languages: (json['languages'] as List? ?? [])
            .map((e) => e is Map<String, dynamic>
                ? LanguageModel.fromJson(e)
                : e as LanguageModel)
            .cast<LanguageModel>()
            .toList(),
        certifications: (json['certifications'] as List? ?? [])
            .map((e) => e is Map<String, dynamic>
                ? CertificationModel.fromJson(e)
                : e as CertificationModel)
            .cast<CertificationModel>()
            .toList(),
        socialMedia: (json['socialMedia'] as List? ?? [])
            .map((e) => e is Map<String, dynamic>
                ? SocialMediaModel.fromJson(e)
                : e as SocialMediaModel)
            .cast<SocialMediaModel>()
            .toList(),
        createdAt: _parseDateTime(json['createdAt']) ?? DateTime.now(),
        updatedAt: _parseDateTime(json['updatedAt']) ?? DateTime.now(),
      );
    } catch (e) {
      print('Error parsing ResumeModel: $e');
      print('JSON data: $json');
      rethrow;
    }
  }

  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;
    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        print('Error parsing date string: $value');
        return null;
      }
    }
    if (value is int) {
      try {
        return DateTime.fromMillisecondsSinceEpoch(value);
      } catch (e) {
        print('Error parsing date timestamp: $value');
        return null;
      }
    }
    print('Unknown date format: $value (${value.runtimeType})');
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'personalInfo': personalInfo.toJson(),
      'summary': summary,
      'workExperience': workExperience.map((e) => e.toJson()).toList(),
      'education': education.map((e) => e.toJson()).toList(),
      'projects': projects.map((e) => e.toJson()).toList(),
      'skills': skills.map((e) => e.toJson()).toList(),
      'languages': languages.map((e) => e.toJson()).toList(),
      'certifications': certifications.map((e) => e.toJson()).toList(),
      'socialMedia': socialMedia.map((e) => e.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  ResumeModel copyWith({
    String? id,
    PersonalInfoModel? personalInfo,
    String? summary,
    List<WorkExperienceModel>? workExperience,
    List<EducationModel>? education,
    List<ProjectModel>? projects,
    List<SkillCategoryModel>? skills,
    List<LanguageModel>? languages,
    List<CertificationModel>? certifications,
    List<SocialMediaModel>? socialMedia,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ResumeModel(
      id: id ?? this.id,
      personalInfo: personalInfo ?? this.personalInfo,
      summary: summary ?? this.summary,
      workExperience: workExperience ?? this.workExperience,
      education: education ?? this.education,
      projects: projects ?? this.projects,
      skills: skills ?? this.skills,
      languages: languages ?? this.languages,
      certifications: certifications ?? this.certifications,
      socialMedia: socialMedia ?? this.socialMedia,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        personalInfo,
        summary,
        workExperience,
        education,
        projects,
        skills,
        languages,
        certifications,
        socialMedia,
        createdAt,
        updatedAt,
      ];
}

class PersonalInfoModel extends Equatable {
  final String firstName;
  final String lastName;
  final String email;
  final String phone;
  final String address;
  final String city;
  final String state;
  final String zipCode;
  final String country;
  final String? profileImageUrl;

  const PersonalInfoModel({
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
    required this.address,
    required this.city,
    required this.state,
    required this.zipCode,
    required this.country,
    this.profileImageUrl,
  });

  String get fullName => '$firstName $lastName';

  factory PersonalInfoModel.fromJson(Map<String, dynamic> json) {
    try {
      return PersonalInfoModel(
        firstName: json['firstName'] as String? ?? '',
        lastName: json['lastName'] as String? ?? '',
        email: json['email'] as String? ?? '',
        phone: json['phone'] as String? ?? '',
        address: json['address'] as String? ?? '',
        city: json['city'] as String? ?? '',
        state: json['state'] as String? ?? '',
        zipCode: json['zipCode']?.toString() ?? '', // Convert to string safely
        country: json['country'] as String? ?? '',
        profileImageUrl: json['profileImageUrl'] as String?,
      );
    } catch (e) {
      print('Error parsing PersonalInfoModel: $e');
      print('JSON data: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'phone': phone,
      'address': address,
      'city': city,
      'state': state,
      'zipCode': zipCode,
      'country': country,
      'profileImageUrl': profileImageUrl,
    };
  }

  PersonalInfoModel copyWith({
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? address,
    String? city,
    String? state,
    String? zipCode,
    String? country,
    String? profileImageUrl,
  }) {
    return PersonalInfoModel(
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      zipCode: zipCode ?? this.zipCode,
      country: country ?? this.country,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
    );
  }

  @override
  List<Object?> get props => [
        firstName,
        lastName,
        email,
        phone,
        address,
        city,
        state,
        zipCode,
        country,
        profileImageUrl,
      ];
}

class WorkExperienceModel extends Equatable {
  final String id;
  final String jobTitle;
  final String company;
  final String location;
  final DateTime startDate;
  final DateTime? endDate;
  final bool isCurrentJob;
  final String description;
  final List<String> achievements;

  const WorkExperienceModel({
    required this.id,
    required this.jobTitle,
    required this.company,
    required this.location,
    required this.startDate,
    this.endDate,
    required this.isCurrentJob,
    required this.description,
    required this.achievements,
  });

  factory WorkExperienceModel.fromJson(Map<String, dynamic> json) {
    try {
      return WorkExperienceModel(
        id: json['id'] as String? ?? '',
        jobTitle: json['jobTitle'] as String? ?? '',
        company: json['company'] as String? ?? '',
        location: json['location'] as String? ?? '',
        startDate: _parseDateTime(json['startDate']) ?? DateTime.now(),
        endDate: _parseDateTime(json['endDate']),
        isCurrentJob: json['isCurrentJob'] as bool? ?? false,
        description: json['description'] as String? ?? '',
        achievements: List<String>.from(json['achievements'] as List? ?? []),
      );
    } catch (e) {
      print('Error parsing WorkExperienceModel: $e');
      print('JSON data: $json');
      rethrow;
    }
  }

  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;
    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        print('Error parsing date string: $value');
        return null;
      }
    }
    if (value is int) {
      try {
        return DateTime.fromMillisecondsSinceEpoch(value);
      } catch (e) {
        print('Error parsing date timestamp: $value');
        return null;
      }
    }
    print('Unknown date format: $value (${value.runtimeType})');
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'jobTitle': jobTitle,
      'company': company,
      'location': location,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'isCurrentJob': isCurrentJob,
      'description': description,
      'achievements': achievements,
    };
  }

  WorkExperienceModel copyWith({
    String? id,
    String? jobTitle,
    String? company,
    String? location,
    DateTime? startDate,
    DateTime? endDate,
    bool? isCurrentJob,
    String? description,
    List<String>? achievements,
  }) {
    return WorkExperienceModel(
      id: id ?? this.id,
      jobTitle: jobTitle ?? this.jobTitle,
      company: company ?? this.company,
      location: location ?? this.location,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isCurrentJob: isCurrentJob ?? this.isCurrentJob,
      description: description ?? this.description,
      achievements: achievements ?? this.achievements,
    );
  }

  @override
  List<Object?> get props => [
        id,
        jobTitle,
        company,
        location,
        startDate,
        endDate,
        isCurrentJob,
        description,
        achievements,
      ];
}

class EducationModel extends Equatable {
  final String id;
  final String degree;
  final String institution;
  final String location;
  final DateTime startDate;
  final DateTime? endDate;
  final bool isCurrentlyStudying;
  final String? gpa;
  final String? description;

  const EducationModel({
    required this.id,
    required this.degree,
    required this.institution,
    required this.location,
    required this.startDate,
    this.endDate,
    required this.isCurrentlyStudying,
    this.gpa,
    this.description,
  });

  factory EducationModel.fromJson(Map<String, dynamic> json) {
    return EducationModel(
      id: json['id'] as String,
      degree: json['degree'] as String,
      institution: json['institution'] as String,
      location: json['location'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate'] as String) : null,
      isCurrentlyStudying: json['isCurrentlyStudying'] as bool,
      gpa: json['gpa'] as String?,
      description: json['description'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'degree': degree,
      'institution': institution,
      'location': location,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'isCurrentlyStudying': isCurrentlyStudying,
      'gpa': gpa,
      'description': description,
    };
  }

  EducationModel copyWith({
    String? id,
    String? degree,
    String? institution,
    String? location,
    DateTime? startDate,
    DateTime? endDate,
    bool? isCurrentlyStudying,
    String? gpa,
    String? description,
  }) {
    return EducationModel(
      id: id ?? this.id,
      degree: degree ?? this.degree,
      institution: institution ?? this.institution,
      location: location ?? this.location,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isCurrentlyStudying: isCurrentlyStudying ?? this.isCurrentlyStudying,
      gpa: gpa ?? this.gpa,
      description: description ?? this.description,
    );
  }

  @override
  List<Object?> get props => [
        id,
        degree,
        institution,
        location,
        startDate,
        endDate,
        isCurrentlyStudying,
        gpa,
        description,
      ];
}

class ProjectModel extends Equatable {
  final String id;
  final String name;
  final String description;
  final List<String> technologies;
  final String? projectUrl;
  final String? githubUrl;
  final DateTime? startDate;
  final DateTime? endDate;
  final List<String> achievements;

  const ProjectModel({
    required this.id,
    required this.name,
    required this.description,
    required this.technologies,
    this.projectUrl,
    this.githubUrl,
    this.startDate,
    this.endDate,
    required this.achievements,
  });

  factory ProjectModel.fromJson(Map<String, dynamic> json) {
    return ProjectModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      technologies: List<String>.from(json['technologies'] as List? ?? []),
      projectUrl: json['projectUrl'] as String?,
      githubUrl: json['githubUrl'] as String?,
      startDate: json['startDate'] != null ? DateTime.parse(json['startDate'] as String) : null,
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate'] as String) : null,
      achievements: List<String>.from(json['achievements'] as List? ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'technologies': technologies,
      'projectUrl': projectUrl,
      'githubUrl': githubUrl,
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'achievements': achievements,
    };
  }

  ProjectModel copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? technologies,
    String? projectUrl,
    String? githubUrl,
    DateTime? startDate,
    DateTime? endDate,
    List<String>? achievements,
  }) {
    return ProjectModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      technologies: technologies ?? this.technologies,
      projectUrl: projectUrl ?? this.projectUrl,
      githubUrl: githubUrl ?? this.githubUrl,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      achievements: achievements ?? this.achievements,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        technologies,
        projectUrl,
        githubUrl,
        startDate,
        endDate,
        achievements,
      ];
}

class SkillCategoryModel extends Equatable {
  final String id;
  final String category;
  final List<SkillModel> skills;

  const SkillCategoryModel({
    required this.id,
    required this.category,
    required this.skills,
  });

  factory SkillCategoryModel.fromJson(Map<String, dynamic> json) {
    return SkillCategoryModel(
      id: json['id'] as String,
      category: json['category'] as String,
      skills: (json['skills'] as List? ?? [])
          .map((e) => SkillModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category': category,
      'skills': skills.map((e) => e.toJson()).toList(),
    };
  }

  SkillCategoryModel copyWith({
    String? id,
    String? category,
    List<SkillModel>? skills,
  }) {
    return SkillCategoryModel(
      id: id ?? this.id,
      category: category ?? this.category,
      skills: skills ?? this.skills,
    );
  }

  @override
  List<Object?> get props => [id, category, skills];
}

class SkillModel extends Equatable {
  final String id;
  final String name;
  final int proficiencyLevel; // 1-5 scale

  const SkillModel({
    required this.id,
    required this.name,
    required this.proficiencyLevel,
  });

  factory SkillModel.fromJson(Map<String, dynamic> json) {
    return SkillModel(
      id: json['id'] as String? ?? '',
      name: json['name'] as String? ?? '',
      proficiencyLevel: json['proficiencyLevel'] as int? ??
                       (json['level'] != null ? _levelToInt(json['level'] as String) : 1),
    );
  }

  static int _levelToInt(String level) {
    switch (level.toLowerCase()) {
      case 'beginner':
        return 1;
      case 'intermediate':
        return 2;
      case 'advanced':
        return 3;
      case 'expert':
        return 4;
      default:
        return 2; // Default to intermediate
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'proficiencyLevel': proficiencyLevel,
    };
  }

  SkillModel copyWith({
    String? id,
    String? name,
    int? proficiencyLevel,
  }) {
    return SkillModel(
      id: id ?? this.id,
      name: name ?? this.name,
      proficiencyLevel: proficiencyLevel ?? this.proficiencyLevel,
    );
  }

  @override
  List<Object?> get props => [id, name, proficiencyLevel];
}

class LanguageModel extends Equatable {
  final String id;
  final String language;
  final String proficiency; // Beginner, Intermediate, Advanced, Native

  const LanguageModel({
    required this.id,
    required this.language,
    required this.proficiency,
  });

  factory LanguageModel.fromJson(Map<String, dynamic> json) {
    return LanguageModel(
      id: json['id'] as String,
      language: json['language'] as String,
      proficiency: json['proficiency'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'language': language,
      'proficiency': proficiency,
    };
  }

  LanguageModel copyWith({
    String? id,
    String? language,
    String? proficiency,
  }) {
    return LanguageModel(
      id: id ?? this.id,
      language: language ?? this.language,
      proficiency: proficiency ?? this.proficiency,
    );
  }

  @override
  List<Object?> get props => [id, language, proficiency];
}

class CertificationModel extends Equatable {
  final String id;
  final String name;
  final String issuer;
  final DateTime issueDate;
  final DateTime? expiryDate;
  final String? credentialId;
  final String? credentialUrl;

  const CertificationModel({
    required this.id,
    required this.name,
    required this.issuer,
    required this.issueDate,
    this.expiryDate,
    this.credentialId,
    this.credentialUrl,
  });

  factory CertificationModel.fromJson(Map<String, dynamic> json) {
    return CertificationModel(
      id: json['id'] as String,
      name: json['name'] as String,
      issuer: json['issuer'] as String,
      issueDate: DateTime.parse(json['issueDate'] as String),
      expiryDate: json['expiryDate'] != null ? DateTime.parse(json['expiryDate'] as String) : null,
      credentialId: json['credentialId'] as String?,
      credentialUrl: json['credentialUrl'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'issuer': issuer,
      'issueDate': issueDate.toIso8601String(),
      'expiryDate': expiryDate?.toIso8601String(),
      'credentialId': credentialId,
      'credentialUrl': credentialUrl,
    };
  }

  CertificationModel copyWith({
    String? id,
    String? name,
    String? issuer,
    DateTime? issueDate,
    DateTime? expiryDate,
    String? credentialId,
    String? credentialUrl,
  }) {
    return CertificationModel(
      id: id ?? this.id,
      name: name ?? this.name,
      issuer: issuer ?? this.issuer,
      issueDate: issueDate ?? this.issueDate,
      expiryDate: expiryDate ?? this.expiryDate,
      credentialId: credentialId ?? this.credentialId,
      credentialUrl: credentialUrl ?? this.credentialUrl,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        issuer,
        issueDate,
        expiryDate,
        credentialId,
        credentialUrl,
      ];
}

class SocialMediaModel extends Equatable {
  final String id;
  final String platform;
  final String url;
  final String username;

  const SocialMediaModel({
    required this.id,
    required this.platform,
    required this.url,
    required this.username,
  });

  factory SocialMediaModel.fromJson(Map<String, dynamic> json) {
    return SocialMediaModel(
      id: json['id'] as String,
      platform: json['platform'] as String,
      url: json['url'] as String,
      username: json['username'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'platform': platform,
      'url': url,
      'username': username,
    };
  }

  SocialMediaModel copyWith({
    String? id,
    String? platform,
    String? url,
    String? username,
  }) {
    return SocialMediaModel(
      id: id ?? this.id,
      platform: platform ?? this.platform,
      url: url ?? this.url,
      username: username ?? this.username,
    );
  }

  @override
  List<Object?> get props => [id, platform, url, username];
}
