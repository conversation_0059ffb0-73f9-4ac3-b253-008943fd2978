import 'package:equatable/equatable.dart';

enum ActivityType {
  resumeCreated,
  resumeUpdated,
  resumeSaved,
  resumeExported,
  sectionEdited,
  templateUsed,
  resumeViewed,
  resumeDeleted,
}

class ActivityModel extends Equatable {
  final String id;
  final ActivityType type;
  final String title;
  final String description;
  final DateTime timestamp;
  final String? resumeId;
  final String? resumeName;
  final String? sectionName;
  final Map<String, dynamic>? metadata;

  const ActivityModel({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.timestamp,
    this.resumeId,
    this.resumeName,
    this.sectionName,
    this.metadata,
  });

  factory ActivityModel.fromJson(Map<String, dynamic> json) {
    return ActivityModel(
      id: json['id'] as String,
      type: ActivityType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => ActivityType.resumeUpdated,
      ),
      title: json['title'] as String,
      description: json['description'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      resumeId: json['resumeId'] as String?,
      resumeName: json['resumeName'] as String?,
      sectionName: json['sectionName'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString(),
      'title': title,
      'description': description,
      'timestamp': timestamp.toIso8601String(),
      'resumeId': resumeId,
      'resumeName': resumeName,
      'sectionName': sectionName,
      'metadata': metadata,
    };
  }

  ActivityModel copyWith({
    String? id,
    ActivityType? type,
    String? title,
    String? description,
    DateTime? timestamp,
    String? resumeId,
    String? resumeName,
    String? sectionName,
    Map<String, dynamic>? metadata,
  }) {
    return ActivityModel(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      timestamp: timestamp ?? this.timestamp,
      resumeId: resumeId ?? this.resumeId,
      resumeName: resumeName ?? this.resumeName,
      sectionName: sectionName ?? this.sectionName,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        type,
        title,
        description,
        timestamp,
        resumeId,
        resumeName,
        sectionName,
        metadata,
      ];
}

// Helper class for creating activities
class ActivityFactory {
  static ActivityModel createResumeActivity({
    required String id,
    required ActivityType type,
    required String resumeId,
    required String resumeName,
    String? sectionName,
    Map<String, dynamic>? metadata,
  }) {
    String title;
    String description;

    switch (type) {
      case ActivityType.resumeCreated:
        title = 'Resume Created';
        description = 'Created new resume "$resumeName"';
        break;
      case ActivityType.resumeUpdated:
        title = 'Resume Updated';
        description = sectionName != null
            ? 'Updated $sectionName section in "$resumeName"'
            : 'Updated resume "$resumeName"';
        break;
      case ActivityType.resumeSaved:
        title = 'Resume Saved';
        description = 'Saved changes to "$resumeName"';
        break;
      case ActivityType.resumeExported:
        title = 'Resume Exported';
        description = 'Exported "$resumeName" to PDF';
        break;
      case ActivityType.sectionEdited:
        title = 'Section Edited';
        description = 'Edited $sectionName section in "$resumeName"';
        break;
      case ActivityType.templateUsed:
        title = 'Template Applied';
        description = 'Applied template to "$resumeName"';
        break;
      case ActivityType.resumeViewed:
        title = 'Resume Viewed';
        description = 'Opened resume "$resumeName"';
        break;
      case ActivityType.resumeDeleted:
        title = 'Resume Deleted';
        description = 'Deleted resume "$resumeName"';
        break;
    }

    return ActivityModel(
      id: id,
      type: type,
      title: title,
      description: description,
      timestamp: DateTime.now(),
      resumeId: resumeId,
      resumeName: resumeName,
      sectionName: sectionName,
      metadata: metadata,
    );
  }
}
