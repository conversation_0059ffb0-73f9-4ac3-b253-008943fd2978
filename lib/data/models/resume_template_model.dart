import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import 'simple_resume_model.dart';

enum TemplateCategory {
  professional,
  creative,
  modern,
  minimal,
}

class ResumeTemplateModel extends Equatable {
  final String id;
  final String name;
  final String description;
  final IconData previewIcon;
  final TemplateCategory category;
  final bool isPremium;
  final TemplateStyle style;

  const ResumeTemplateModel({
    required this.id,
    required this.name,
    required this.description,
    required this.previewIcon,
    required this.category,
    required this.isPremium,
    required this.style,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        previewIcon,
        category,
        isPremium,
        style,
      ];
}

class TemplateStyle extends Equatable {
  final Color primaryColor;
  final Color secondaryColor;
  final Color accentColor;
  final String fontFamily;
  final double headerFontSize;
  final double bodyFontSize;
  final double sectionSpacing;
  final bool showProfileImage;
  final bool showSectionIcons;
  final TemplateLayout layout;

  const TemplateStyle({
    required this.primaryColor,
    required this.secondaryColor,
    required this.accentColor,
    required this.fontFamily,
    required this.headerFontSize,
    required this.bodyFontSize,
    required this.sectionSpacing,
    required this.showProfileImage,
    required this.showSectionIcons,
    required this.layout,
  });

  @override
  List<Object?> get props => [
        primaryColor,
        secondaryColor,
        accentColor,
        fontFamily,
        headerFontSize,
        bodyFontSize,
        sectionSpacing,
        showProfileImage,
        showSectionIcons,
        layout,
      ];
}

enum TemplateLayout {
  singleColumn,
  twoColumn,
  sidebar,
  modern,
}

// Template data repository
class TemplateRepository {
  static const String defaultTemplateId = 'classic_professional';

  static List<ResumeTemplateModel> getAllTemplates() {
    return [
      // Professional Templates
      ResumeTemplateModel(
        id: 'classic_professional',
        name: 'Classic Professional',
        description: 'Clean and professional design perfect for corporate roles',
        previewIcon: Icons.business_center,
        category: TemplateCategory.professional,
        isPremium: false,
        style: const TemplateStyle(
          primaryColor: Color(0xFF2C3E50),
          secondaryColor: Color(0xFF34495E),
          accentColor: Color(0xFF3498DB),
          fontFamily: 'Roboto',
          headerFontSize: 24.0,
          bodyFontSize: 14.0,
          sectionSpacing: 20.0,
          showProfileImage: true,
          showSectionIcons: false,
          layout: TemplateLayout.singleColumn,
        ),
      ),
      ResumeTemplateModel(
        id: 'executive_blue',
        name: 'Executive Blue',
        description: 'Sophisticated blue theme for senior positions',
        previewIcon: Icons.corporate_fare,
        category: TemplateCategory.professional,
        isPremium: true,
        style: const TemplateStyle(
          primaryColor: Color(0xFF1E3A8A),
          secondaryColor: Color(0xFF3B82F6),
          accentColor: Color(0xFF60A5FA),
          fontFamily: 'Inter',
          headerFontSize: 26.0,
          bodyFontSize: 14.0,
          sectionSpacing: 24.0,
          showProfileImage: true,
          showSectionIcons: true,
          layout: TemplateLayout.twoColumn,
        ),
      ),
      
      // Modern Templates
      ResumeTemplateModel(
        id: 'modern_gradient',
        name: 'Modern Gradient',
        description: 'Contemporary design with gradient accents',
        previewIcon: Icons.gradient,
        category: TemplateCategory.modern,
        isPremium: false,
        style: const TemplateStyle(
          primaryColor: Color(0xFF6366F1),
          secondaryColor: Color(0xFF8B5CF6),
          accentColor: Color(0xFFA855F7),
          fontFamily: 'Inter',
          headerFontSize: 28.0,
          bodyFontSize: 15.0,
          sectionSpacing: 22.0,
          showProfileImage: true,
          showSectionIcons: true,
          layout: TemplateLayout.modern,
        ),
      ),
      ResumeTemplateModel(
        id: 'tech_stack',
        name: 'Tech Stack',
        description: 'Perfect for developers and tech professionals',
        previewIcon: Icons.code,
        category: TemplateCategory.modern,
        isPremium: true,
        style: const TemplateStyle(
          primaryColor: Color(0xFF0F172A),
          secondaryColor: Color(0xFF1E293B),
          accentColor: Color(0xFF10B981),
          fontFamily: 'JetBrains Mono',
          headerFontSize: 24.0,
          bodyFontSize: 13.0,
          sectionSpacing: 18.0,
          showProfileImage: false,
          showSectionIcons: true,
          layout: TemplateLayout.sidebar,
        ),
      ),
      
      // Creative Templates
      ResumeTemplateModel(
        id: 'creative_orange',
        name: 'Creative Orange',
        description: 'Bold and creative design for artistic roles',
        previewIcon: Icons.palette,
        category: TemplateCategory.creative,
        isPremium: false,
        style: const TemplateStyle(
          primaryColor: Color(0xFFEA580C),
          secondaryColor: Color(0xFFFB923C),
          accentColor: Color(0xFFFED7AA),
          fontFamily: 'Poppins',
          headerFontSize: 30.0,
          bodyFontSize: 14.0,
          sectionSpacing: 26.0,
          showProfileImage: true,
          showSectionIcons: true,
          layout: TemplateLayout.twoColumn,
        ),
      ),
      ResumeTemplateModel(
        id: 'artistic_purple',
        name: 'Artistic Purple',
        description: 'Elegant purple theme for creative professionals',
        previewIcon: Icons.brush,
        category: TemplateCategory.creative,
        isPremium: true,
        style: const TemplateStyle(
          primaryColor: Color(0xFF7C3AED),
          secondaryColor: Color(0xFF8B5CF6),
          accentColor: Color(0xFFC4B5FD),
          fontFamily: 'Playfair Display',
          headerFontSize: 32.0,
          bodyFontSize: 15.0,
          sectionSpacing: 28.0,
          showProfileImage: true,
          showSectionIcons: false,
          layout: TemplateLayout.modern,
        ),
      ),
      
      // Minimal Templates
      ResumeTemplateModel(
        id: 'minimal_black',
        name: 'Minimal Black',
        description: 'Clean minimal design with black accents',
        previewIcon: Icons.minimize,
        category: TemplateCategory.minimal,
        isPremium: false,
        style: const TemplateStyle(
          primaryColor: Color(0xFF000000),
          secondaryColor: Color(0xFF374151),
          accentColor: Color(0xFF6B7280),
          fontFamily: 'Source Sans Pro',
          headerFontSize: 22.0,
          bodyFontSize: 13.0,
          sectionSpacing: 16.0,
          showProfileImage: false,
          showSectionIcons: false,
          layout: TemplateLayout.singleColumn,
        ),
      ),
      ResumeTemplateModel(
        id: 'minimal_green',
        name: 'Minimal Green',
        description: 'Subtle green accents for a fresh look',
        previewIcon: Icons.eco,
        category: TemplateCategory.minimal,
        isPremium: true,
        style: const TemplateStyle(
          primaryColor: Color(0xFF059669),
          secondaryColor: Color(0xFF10B981),
          accentColor: Color(0xFF6EE7B7),
          fontFamily: 'Lato',
          headerFontSize: 24.0,
          bodyFontSize: 14.0,
          sectionSpacing: 18.0,
          showProfileImage: false,
          showSectionIcons: false,
          layout: TemplateLayout.singleColumn,
        ),
      ),
    ];
  }

  static ResumeTemplateModel getTemplateById(String id) {
    return getAllTemplates().firstWhere(
      (template) => template.id == id,
      orElse: () => getAllTemplates().first,
    );
  }

  static ResumeTemplateModel getDefaultTemplate() {
    return getTemplateById(defaultTemplateId);
  }

  static List<ResumeTemplateModel> getTemplatesByCategory(TemplateCategory category) {
    return getAllTemplates().where((template) => template.category == category).toList();
  }

  static List<ResumeTemplateModel> getFreeTemplates() {
    return getAllTemplates().where((template) => !template.isPremium).toList();
  }

  static List<ResumeTemplateModel> getPremiumTemplates() {
    return getAllTemplates().where((template) => template.isPremium).toList();
  }

  // Sample resume data for template previews
  static ResumeModel getSampleResumeData() {
    return ResumeModel(
      id: 'sample-preview',
      personalInfo: const PersonalInfoModel(
        firstName: 'Sarah',
        lastName: 'Johnson',
        email: '<EMAIL>',
        phone: '+****************',
        address: '123 Tech Street',
        city: 'San Francisco',
        state: 'CA',
        zipCode: '94105',
        country: 'United States',
      ),
      summary: 'Experienced Full-Stack Developer with 5+ years of expertise in mobile and web application development. Proven track record of delivering scalable solutions using modern technologies.',
      workExperience: [
        WorkExperienceModel(
          id: 'work-1',
          jobTitle: 'Senior Mobile Developer',
          company: 'TechCorp Solutions',
          location: 'San Francisco, CA',
          startDate: DateTime(2022, 3, 1),
          endDate: null,
          isCurrentJob: true,
          description: 'Lead mobile development team in creating cross-platform applications using Flutter and React Native.',
          achievements: [
            'Developed and launched 3 mobile apps with 100K+ downloads each',
            'Improved app performance by 40% through optimization',
            'Led team of 5 developers across multiple projects'
          ],
        ),
        WorkExperienceModel(
          id: 'work-2',
          jobTitle: 'Full-Stack Developer',
          company: 'StartupXYZ',
          location: 'San Francisco, CA',
          startDate: DateTime(2020, 1, 1),
          endDate: DateTime(2022, 2, 28),
          isCurrentJob: false,
          description: 'Developed web applications using React, Node.js, and PostgreSQL.',
          achievements: [
            'Built customer portal serving 50K+ users',
            'Reduced server response time by 60%',
            'Implemented automated testing pipeline'
          ],
        ),
      ],
      education: [
        EducationModel(
          id: 'edu-1',
          degree: 'Bachelor of Science in Computer Science',
          institution: 'University of California, Berkeley',
          location: 'Berkeley, CA',
          startDate: DateTime(2016, 9, 1),
          endDate: DateTime(2020, 5, 31),
          isCurrentlyStudying: false,
          gpa: '3.8',
          description: 'Focused on software engineering and mobile development',
        ),
      ],
      projects: [
        ProjectModel(
          id: 'project-1',
          name: 'E-Commerce Mobile App',
          description: 'Cross-platform mobile application for online shopping with real-time inventory management.',
          technologies: ['Flutter', 'Firebase', 'Stripe API', 'Node.js'],
          achievements: [
            'Achieved 4.8-star rating on app stores',
            'Processed over \$1M in transactions',
            'Integrated with 10+ payment providers'
          ],
        ),
        ProjectModel(
          id: 'project-2',
          name: 'Task Management Dashboard',
          description: 'Web-based project management tool with real-time collaboration features.',
          technologies: ['React', 'TypeScript', 'GraphQL', 'PostgreSQL'],
          achievements: [
            'Increased team productivity by 35%',
            'Supports 1000+ concurrent users',
            'Deployed across 5 different companies'
          ],
        ),
      ],
      skills: [
        SkillCategoryModel(
          id: 'skills-1',
          category: 'Programming Languages',
          skills: [
            SkillModel(id: 'skill-1', name: 'Dart/Flutter', proficiencyLevel: 5),
            SkillModel(id: 'skill-2', name: 'JavaScript/TypeScript', proficiencyLevel: 5),
            SkillModel(id: 'skill-3', name: 'Python', proficiencyLevel: 4),
            SkillModel(id: 'skill-4', name: 'Java', proficiencyLevel: 4),
          ],
        ),
        SkillCategoryModel(
          id: 'skills-2',
          category: 'Frameworks & Tools',
          skills: [
            SkillModel(id: 'skill-5', name: 'React/React Native', proficiencyLevel: 5),
            SkillModel(id: 'skill-6', name: 'Node.js', proficiencyLevel: 4),
            SkillModel(id: 'skill-7', name: 'Firebase', proficiencyLevel: 4),
            SkillModel(id: 'skill-8', name: 'AWS', proficiencyLevel: 3),
          ],
        ),
      ],
      languages: [
        LanguageModel(id: 'lang-1', language: 'English', proficiency: 'Native'),
        LanguageModel(id: 'lang-2', language: 'Spanish', proficiency: 'Conversational'),
      ],
      certifications: [
        CertificationModel(
          id: 'cert-1',
          name: 'AWS Certified Developer',
          issuer: 'Amazon Web Services',
          issueDate: DateTime(2023, 6, 15),
          expiryDate: DateTime(2026, 6, 15),
          credentialId: 'AWS-DEV-2023-001',
        ),
      ],
      socialMedia: [
        SocialMediaModel(
          id: 'social-1',
          platform: 'GitHub',
          url: 'https://github.com/sarahjohnson',
          username: 'sarahjohnson',
        ),
        SocialMediaModel(
          id: 'social-2',
          platform: 'LinkedIn',
          url: 'https://linkedin.com/in/sarahjohnson',
          username: 'sarahjohnson',
        ),
      ],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}
