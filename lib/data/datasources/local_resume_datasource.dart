import 'package:hive/hive.dart';
import '../models/simple_resume_model.dart';
import '../../core/constants/app_constants.dart';

class LocalResumeDataSource {
  Future<Box> _getResumeBox() async {
    return await Hive.openBox(AppConstants.resumeBox);
  }

  Future<void> saveResume(ResumeModel resume) async {
    try {
      final box = await _getResumeBox();

      // Save the current resume
      await box.put(AppConstants.resumeDataKey, resume.toJson());

      // Also save to a list of all resumes for the user
      final existingResumes = await getAllResumes();
      final updatedResumes = existingResumes.where((r) => r.id != resume.id).toList();
      updatedResumes.add(resume);

      await box.put('all_resumes', updatedResumes.map((r) => r.toJson()).toList());
    } catch (e) {
      throw Exception('Failed to save resume locally: $e');
    }
  }

  Future<ResumeModel?> getResume() async {
    try {
      final box = await _getResumeBox();
      final resumeData = box.get(AppConstants.resumeDataKey);

      if (resumeData != null) {
        // Ensure proper type casting from Map<dynamic, dynamic> to Map<String, dynamic>
        final Map<String, dynamic> typedData = _convertToStringDynamicMap(resumeData);
        return ResumeModel.fromJson(typedData);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get local resume: $e');
    }
  }

  Future<List<ResumeModel>> getAllResumes() async {
    try {
      final box = await _getResumeBox();
      final resumesData = box.get('all_resumes');

      if (resumesData != null) {
        final List<dynamic> resumesList = resumesData;
        return resumesList
            .map((data) {
              // Ensure proper type casting from Map<dynamic, dynamic> to Map<String, dynamic>
              final Map<String, dynamic> typedData = _convertToStringDynamicMap(data);
              return ResumeModel.fromJson(typedData);
            })
            .toList();
      }
      return [];
    } catch (e) {
      throw Exception('Failed to get local resumes: $e');
    }
  }

  Future<void> deleteResume() async {
    try {
      final box = await _getResumeBox();
      await box.delete(AppConstants.resumeDataKey);
    } catch (e) {
      throw Exception('Failed to delete local resume: $e');
    }
  }

  Future<void> clearAllData() async {
    try {
      final box = await _getResumeBox();
      await box.clear();
    } catch (e) {
      throw Exception('Failed to clear local data: $e');
    }
  }

  /// Helper method to convert `Map<dynamic, dynamic>` to `Map<String, dynamic>`
  /// This is needed because Hive returns `Map<dynamic, dynamic>` but our models expect `Map<String, dynamic>`
  Map<String, dynamic> _convertToStringDynamicMap(dynamic data) {
    if (data is Map<String, dynamic>) {
      return data;
    } else if (data is Map) {
      return Map<String, dynamic>.from(data.map((key, value) {
        // Recursively convert nested maps
        if (value is Map) {
          return MapEntry(key.toString(), _convertToStringDynamicMap(value));
        } else if (value is List) {
          return MapEntry(key.toString(), _convertListItems(value));
        } else {
          return MapEntry(key.toString(), value);
        }
      }));
    } else {
      throw Exception('Expected Map but got ${data.runtimeType}');
    }
  }

  /// Helper method to convert list items that might contain maps
  List<dynamic> _convertListItems(List<dynamic> list) {
    return list.map((item) {
      if (item is Map) {
        return _convertToStringDynamicMap(item);
      } else if (item is List) {
        return _convertListItems(item);
      } else {
        return item;
      }
    }).toList();
  }
}
