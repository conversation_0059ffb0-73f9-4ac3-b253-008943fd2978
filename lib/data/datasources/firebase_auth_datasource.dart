import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../models/user_model.dart';
import '../../core/constants/app_constants.dart';

class FirebaseAuthDataSource {
  final FirebaseAuth _firebaseAuth;
  final GoogleSignIn _googleSignIn;
  final FirebaseFirestore _firestore;

  FirebaseAuthDataSource(
    this._firebaseAuth,
    this._googleSignIn,
    this._firestore,
  );

  Future<UserModel?> getCurrentUser() async {
    final user = _firebaseAuth.currentUser;
    if (user != null) {
      return await _getUserFromFirestore(user.uid) ?? _mapFirebaseUserToUserModel(user);
    }
    return null;
  }

  Future<UserModel> signInWithEmail(String email, String password) async {
    try {
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (credential.user != null) {
        final userModel = await _getUserFromFirestore(credential.user!.uid) ??
            _mapFirebaseUserToUserModel(credential.user!);
        await _updateLastSignIn(userModel.id);
        return userModel;
      }
      throw Exception('Sign in failed');
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  Future<UserModel> signUpWithEmail(String email, String password, String displayName) async {
    try {
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (credential.user != null) {
        await credential.user!.updateDisplayName(displayName);
        
        final userModel = UserModel(
          id: credential.user!.uid,
          email: email,
          displayName: displayName,
          emailVerified: credential.user!.emailVerified,
          createdAt: DateTime.now(),
          lastSignIn: DateTime.now(),
          resumeIds: [],
        );
        
        await _saveUserToFirestore(userModel);
        return userModel;
      }
      throw Exception('Sign up failed');
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  Future<UserModel> signInWithGoogle() async {
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        throw Exception('Google sign in was cancelled');
      }

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential = await _firebaseAuth.signInWithCredential(credential);
      
      if (userCredential.user != null) {
        final existingUser = await _getUserFromFirestore(userCredential.user!.uid);
        if (existingUser != null) {
          await _updateLastSignIn(existingUser.id);
          return existingUser;
        } else {
          final userModel = UserModel(
            id: userCredential.user!.uid,
            email: userCredential.user!.email!,
            displayName: userCredential.user!.displayName,
            photoUrl: userCredential.user!.photoURL,
            emailVerified: userCredential.user!.emailVerified,
            createdAt: DateTime.now(),
            lastSignIn: DateTime.now(),
            resumeIds: [],
          );
          
          await _saveUserToFirestore(userModel);
          return userModel;
        }
      }
      throw Exception('Google sign in failed');
    } catch (e) {
      throw Exception('Google sign in failed: $e');
    }
  }

  Future<UserModel> signInWithApple() async {
    try {
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      final oauthCredential = OAuthProvider("apple.com").credential(
        idToken: credential.identityToken,
        accessToken: credential.authorizationCode,
      );

      final userCredential = await _firebaseAuth.signInWithCredential(oauthCredential);
      
      if (userCredential.user != null) {
        final existingUser = await _getUserFromFirestore(userCredential.user!.uid);
        if (existingUser != null) {
          await _updateLastSignIn(existingUser.id);
          return existingUser;
        } else {
          final displayName = credential.givenName != null && credential.familyName != null
              ? '${credential.givenName} ${credential.familyName}'
              : userCredential.user!.displayName;
          
          final userModel = UserModel(
            id: userCredential.user!.uid,
            email: credential.email ?? userCredential.user!.email!,
            displayName: displayName,
            emailVerified: userCredential.user!.emailVerified,
            createdAt: DateTime.now(),
            lastSignIn: DateTime.now(),
            resumeIds: [],
          );
          
          await _saveUserToFirestore(userModel);
          return userModel;
        }
      }
      throw Exception('Apple sign in failed');
    } catch (e) {
      throw Exception('Apple sign in failed: $e');
    }
  }

  Future<void> signOut() async {
    await Future.wait([
      _firebaseAuth.signOut(),
      _googleSignIn.signOut(),
    ]);
  }

  Future<void> resetPassword(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  Future<void> updateProfile(String displayName, String? photoUrl) async {
    final user = _firebaseAuth.currentUser;
    if (user != null) {
      await user.updateDisplayName(displayName);
      if (photoUrl != null) {
        await user.updatePhotoURL(photoUrl);
      }
      
      // Update in Firestore as well
      await _firestore.collection(AppConstants.usersCollection).doc(user.uid).update({
        'displayName': displayName,
        if (photoUrl != null) 'photoUrl': photoUrl,
      });
    }
  }

  Future<void> deleteAccount() async {
    final user = _firebaseAuth.currentUser;
    if (user != null) {
      // Delete user data from Firestore
      await _firestore.collection(AppConstants.usersCollection).doc(user.uid).delete();
      
      // Delete the user account
      await user.delete();
    }
  }

  // Helper methods
  UserModel _mapFirebaseUserToUserModel(User user) {
    return UserModel(
      id: user.uid,
      email: user.email!,
      displayName: user.displayName,
      photoUrl: user.photoURL,
      emailVerified: user.emailVerified,
      createdAt: DateTime.now(),
      lastSignIn: DateTime.now(),
      resumeIds: [],
    );
  }

  Future<UserModel?> _getUserFromFirestore(String userId) async {
    try {
      final doc = await _firestore.collection(AppConstants.usersCollection).doc(userId).get();
      if (doc.exists) {
        return UserModel.fromJson(doc.data()!);
      }
    } catch (e) {
      // Handle error silently and return null
    }
    return null;
  }

  Future<void> _saveUserToFirestore(UserModel user) async {
    await _firestore.collection(AppConstants.usersCollection).doc(user.id).set(user.toJson());
  }

  Future<void> _updateLastSignIn(String userId) async {
    await _firestore.collection(AppConstants.usersCollection).doc(userId).update({
      'lastSignIn': DateTime.now().toIso8601String(),
    });
  }

  String _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'No user found for that email.';
      case 'wrong-password':
        return 'Wrong password provided.';
      case 'email-already-in-use':
        return 'The account already exists for that email.';
      case 'weak-password':
        return 'The password provided is too weak.';
      case 'invalid-email':
        return 'The email address is not valid.';
      case 'user-disabled':
        return 'This user account has been disabled.';
      case 'too-many-requests':
        return 'Too many requests. Try again later.';
      default:
        return e.message ?? 'An authentication error occurred.';
    }
  }
}
