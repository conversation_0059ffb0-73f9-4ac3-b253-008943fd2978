import '../../domain/repositories/activity_repository.dart';
import '../datasources/local_activity_datasource.dart';
import '../models/activity_model.dart';

class ActivityRepositoryImpl implements ActivityRepository {
  final LocalActivityDataSource _localDataSource;

  ActivityRepositoryImpl(this._localDataSource);

  @override
  Future<void> saveActivity(ActivityModel activity) async {
    return await _localDataSource.saveActivity(activity);
  }

  @override
  Future<List<ActivityModel>> getAllActivities() async {
    return await _localDataSource.getAllActivities();
  }

  @override
  Future<List<ActivityModel>> getRecentActivities({int limit = 20}) async {
    return await _localDataSource.getRecentActivities(limit: limit);
  }

  @override
  Future<List<ActivityModel>> getActivitiesByType(ActivityType type) async {
    return await _localDataSource.getActivitiesByType(type);
  }

  @override
  Future<List<ActivityModel>> getActivitiesByResumeId(String resumeId) async {
    return await _localDataSource.getActivitiesByResumeId(resumeId);
  }

  @override
  Future<List<ActivityModel>> getActivitiesInDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    return await _localDataSource.getActivitiesInDateRange(
      startDate: startDate,
      endDate: endDate,
    );
  }

  @override
  Future<void> deleteActivity(String activityId) async {
    return await _localDataSource.deleteActivity(activityId);
  }

  @override
  Future<void> clearAllActivities() async {
    return await _localDataSource.clearAllActivities();
  }

  @override
  Future<void> clearOldActivities({int keepCount = 50}) async {
    return await _localDataSource.clearOldActivities(keepCount: keepCount);
  }

  @override
  Future<int> getActivityCount() async {
    return await _localDataSource.getActivityCount();
  }

  @override
  Future<ActivityModel?> getLastActivity() async {
    return await _localDataSource.getLastActivity();
  }
}
